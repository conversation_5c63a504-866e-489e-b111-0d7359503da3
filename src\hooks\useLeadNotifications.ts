import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface Lead {
  id: string;
  customer_name: string;
  customer_email: string;
  service_type: string;
  urgency: string;
  created_at: string;
}

export const useLeadNotifications = (businessId: string, onNewLead?: (lead: Lead) => void) => {
  useEffect(() => {
    if (!businessId) return;

    // Subscribe to real-time lead updates
    const channel = supabase
      .channel('leads-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'leads',
          filter: `business_id=eq.${businessId}`,
        },
        (payload) => {
          const newLead = payload.new as Lead;
          
          // Show toast notification
          toast.success(
            `New lead from ${newLead.customer_name}`,
            {
              description: `Service: ${newLead.service_type} | Urgency: ${newLead.urgency}`,
              action: {
                label: 'View',
                onClick: () => onNewLead?.(newLead),
              },
            }
          );

          // Call callback if provided
          onNewLead?.(newLead);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [businessId, onNewLead]);
};

// Email notification function (would typically be a Supabase Edge Function)
export const sendLeadNotificationEmail = async (
  businessEmail: string,
  businessName: string,
  lead: {
    customer_name: string;
    customer_email: string;
    customer_phone?: string;
    service_type: string;
    message?: string;
    urgency: string;
  }
) => {
  try {
    // This would typically call a Supabase Edge Function for email sending
    // For now, we'll just log the email content
    const emailContent = {
      to: businessEmail,
      subject: `New Lead: ${lead.customer_name} - ${lead.service_type}`,
      html: `
        <h2>New Lead for ${businessName}</h2>
        <p><strong>Customer:</strong> ${lead.customer_name}</p>
        <p><strong>Email:</strong> ${lead.customer_email}</p>
        ${lead.customer_phone ? `<p><strong>Phone:</strong> ${lead.customer_phone}</p>` : ''}
        <p><strong>Service Needed:</strong> ${lead.service_type}</p>
        <p><strong>Urgency:</strong> ${lead.urgency}</p>
        ${lead.message ? `<p><strong>Message:</strong> ${lead.message}</p>` : ''}
        <p><strong>Received:</strong> ${new Date().toLocaleString()}</p>
        
        <hr>
        <p>Please respond to this lead as soon as possible to maximize conversion.</p>
        <p>You can manage all your leads in your business dashboard.</p>
      `,
    };

    console.log('Email notification would be sent:', emailContent);
    
    // In a real implementation, you would call:
    // const { error } = await supabase.functions.invoke('send-email', {
    //   body: emailContent
    // });
    
    return { success: true };
  } catch (error) {
    console.error('Error sending lead notification email:', error);
    return { success: false, error };
  }
};

// Lead scoring function
export const calculateLeadScore = (lead: {
  urgency: string;
  budget_range?: string;
  customer_phone?: string;
  message?: string;
  service_type: string;
}) => {
  let score = 0;

  // Urgency scoring
  switch (lead.urgency) {
    case 'high':
      score += 30;
      break;
    case 'medium':
      score += 20;
      break;
    case 'low':
      score += 10;
      break;
  }

  // Budget range scoring
  if (lead.budget_range) {
    if (lead.budget_range.includes('50,000+')) score += 25;
    else if (lead.budget_range.includes('25,000')) score += 20;
    else if (lead.budget_range.includes('10,000')) score += 15;
    else if (lead.budget_range.includes('5,000')) score += 10;
    else score += 5;
  }

  // Contact information completeness
  if (lead.customer_phone) score += 10;
  if (lead.message && lead.message.length > 50) score += 10;

  // Service type scoring (some services are higher value)
  const highValueServices = [
    'Legal Consultation',
    'Software Development',
    'Business Consulting',
    'Property Valuation',
    'System Integration'
  ];
  
  if (highValueServices.some(service => lead.service_type.includes(service))) {
    score += 15;
  }

  return Math.min(score, 100); // Cap at 100
};
