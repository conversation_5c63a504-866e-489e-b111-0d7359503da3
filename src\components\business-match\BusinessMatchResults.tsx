
import React, { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { MapPin, Star, Heart, Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

interface BusinessMatch {
  id: string;
  title: string;
  description: string | null;
  city: string | null;
  category_id: string | null;
  featured: boolean | null;
  verified: boolean | null;
  subscription_tier: string | null;
  images: string[] | null;
  logo_url: string | null;
  website: string | null;
  categories?: {
    name: string;
    slug: string;
  };
  matchScore: number;
}

type BusinessMatchResultsProps = {
  answers: Record<string, string>;
};

const BusinessMatchResults = ({ answers }: BusinessMatchResultsProps) => {
  const { user } = useAuth();
  const [businesses, setBusinesses] = useState<BusinessMatch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [savedBusinesses, setSavedBusinesses] = useState<Set<string>>(new Set());

  useEffect(() => {
    findMatches();
  }, [answers]);

  const findMatches = async () => {
    try {
      // Get all approved businesses with categories
      const { data: businessData, error } = await supabase
        .from('business_listings')
        .select(`
          *,
          categories (
            name,
            slug
          )
        `)
        .eq('status', 'approved')
        .limit(50);

      if (error) throw error;

      // Calculate match scores using the database function
      const matchedBusinesses: BusinessMatch[] = [];

      for (const business of businessData || []) {
        try {
          const { data: scoreData, error: scoreError } = await supabase
            .rpc('calculate_business_match_score', {
              business_row: business,
              customer_answers: answers
            });

          if (scoreError) {
            console.error('Error calculating score for business:', business.id, scoreError);
            continue;
          }

          const matchScore = scoreData || 0;

          // Only include businesses with a reasonable match score
          if (matchScore >= 30) {
            matchedBusinesses.push({
              ...business,
              matchScore: matchScore
            });
          }
        } catch (error) {
          console.error('Error processing business:', business.id, error);
        }
      }

      // Sort by match score (highest first)
      matchedBusinesses.sort((a, b) => b.matchScore - a.matchScore);

      // Take top 10 matches
      setBusinesses(matchedBusinesses.slice(0, 10));

    } catch (error) {
      console.error('Error finding matches:', error);
      toast.error('Failed to find matches. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getMockBusinessMatches = (answers: Record<string, string>): Business[] => {
    // This is a simplified mock algorithm that would be replaced by actual AI matching
    const mockBusinesses = [
      {
        id: "1",
        name: "Ocean Basket",
        category: "Restaurants",
        location: "Cape Town, Western Cape",
        rating: 4.7,
        reviews: 128,
        image: "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
        isPremium: true,
        description: "Seafood restaurant chain offering a variety of fish dishes and platters.",
        matchScore: 98
      },
      {
        id: "2",
        name: "The Capital Hotels",
        category: "Accommodation",
        location: "Sandton, Gauteng",
        rating: 4.9,
        reviews: 213,
        image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        isPremium: true,
        description: "Luxury apartments and hotel rooms in the heart of Sandton's business district.",
        matchScore: 92
      },
      {
        id: "3",
        name: "Woolworths",
        category: "Retail",
        location: "Nationwide",
        rating: 4.5,
        reviews: 876,
        image: "https://images.unsplash.com/photo-1604719312566-8912e9227c6a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
        isPremium: false,
        description: "Quality clothing, food, and home products with stores across South Africa.",
        matchScore: 85
      },
    ];

    return mockBusinesses;
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-slate-800">Finding Your Perfect Match</h2>
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="h-8 w-8 rounded-full border-4 border-t-[#007749] border-r-transparent border-b-transparent border-l-transparent animate-spin"></div>
            <p className="text-slate-600">Our AI is analyzing your preferences...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold mb-6 text-slate-800">Your Personalized Business Matches</h2>
        <p className="text-slate-600 mb-8">
          Based on your preferences, we've found these businesses that match your needs.
        </p>

        <div className="space-y-6">
          {businesses.map((business) => (
            <Card key={business.id} className="overflow-hidden">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/3 h-48 md:h-auto">
                  <img
                    src={business.image}
                    alt={business.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="md:w-2/3 p-6">
                  <div className="flex flex-col md:flex-row md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-1">{business.name}</h3>
                      <p className="text-sm text-slate-500 mb-2">
                        {business.category} • <MapPin className="inline h-4 w-4" /> {business.location}
                      </p>
                    </div>
                    <div className="bg-[#007749] text-white px-3 py-1 rounded-lg flex items-center mt-2 md:mt-0">
                      <span className="font-medium">{business.matchScore}% Match</span>
                    </div>
                  </div>
                  
                  <p className="text-slate-600 my-3">{business.description}</p>
                  
                  <div className="flex items-center mt-4">
                    <div className="flex items-center text-amber-500">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="ml-1 text-sm font-medium">{business.rating}</span>
                    </div>
                    <span className="mx-2 text-slate-300">•</span>
                    <span className="text-sm text-slate-500">{business.reviews} reviews</span>
                    
                    {business.isPremium && (
                      <>
                        <span className="mx-2 text-slate-300">•</span>
                        <Badge className="bg-[#FDB913] text-black">Premium</Badge>
                      </>
                    )}
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-slate-200">
                    <Button asChild className="bg-[#007749] hover:bg-[#006739]">
                      <Link to={`/business/${business.id}`}>View Business</Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
        
        <div className="mt-10 text-center">
          <Button variant="outline" asChild>
            <Link to="/business-match">Start Over</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BusinessMatchResults;
