
import React, { useState } from "react";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

const HeroSection = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchLocation, setSearchLocation] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Searching for:", searchTerm, "in", searchLocation);
    // Here would be the logic to search and navigate to results
  };

  return (
    <div className="relative">
      {/* Video Background */}
      <div className="absolute inset-0 overflow-hidden z-0">
        <video
          className="absolute min-w-full min-h-full object-cover"
          autoPlay
          loop
          muted
          playsInline
        >
          <source src="https://sa360.co.za/wp-content/uploads/2020/01/sa360-banner-video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="absolute inset-0 bg-gradient-to-r from-[#003087]/90 to-[#007749]/80"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 bg-transparent text-white py-20 md:py-32 min-h-[600px] flex items-center">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-3 text-shadow">
              Discover South Africa's Best Businesses
            </h1>
            <div className="mb-3">
              <span className="inline-block bg-[#FDB913] text-black font-bold px-4 py-1 rounded-full text-sm md:text-base">
                SA's first business listing directory with integrated AI
              </span>
            </div>
            <p className="text-lg md:text-xl mb-8 text-white/90 max-w-2xl mx-auto text-shadow-sm">
              Your premier directory for finding exceptional services, restaurants, accommodation, and experiences across South Africa
            </p>

            <form onSubmit={handleSearch} className="max-w-3xl mx-auto">
              <div className="flex flex-col md:flex-row gap-4 p-2 rounded-lg bg-white/10 backdrop-blur-md shadow-lg">
                <div className="flex-1 relative">
                  <Input
                    type="text"
                    placeholder="What are you looking for?"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="h-12 pl-10 bg-white text-black rounded-md w-full border-white/20"
                  />
                  <Search className="absolute left-3 top-3.5 h-5 w-5 text-slate-500" />
                </div>
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="Location (city, province)"
                    value={searchLocation}
                    onChange={(e) => setSearchLocation(e.target.value)}
                    className="h-12 bg-white text-black rounded-md w-full border-white/20"
                  />
                </div>
                <Button type="submit" className="h-12 bg-[#FDB913] hover:bg-[#e9aa12] text-black font-semibold px-8">
                  Search
                </Button>
              </div>
            </form>

            <div className="mt-8 flex flex-wrap justify-center gap-4">
              <span className="text-sm text-white/80">Popular:</span>
              <a href="#" className="text-sm text-white hover:underline bg-white/10 px-3 py-1 rounded-full backdrop-blur-sm">Safari Tours</a>
              <a href="#" className="text-sm text-white hover:underline bg-white/10 px-3 py-1 rounded-full backdrop-blur-sm">Restaurants</a>
              <a href="#" className="text-sm text-white hover:underline bg-white/10 px-3 py-1 rounded-full backdrop-blur-sm">Wine Estates</a>
              <a href="#" className="text-sm text-white hover:underline bg-white/10 px-3 py-1 rounded-full backdrop-blur-sm">Beach Hotels</a>
              <a href="#" className="text-sm text-white hover:underline bg-white/10 px-3 py-1 rounded-full backdrop-blur-sm">Car Services</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
