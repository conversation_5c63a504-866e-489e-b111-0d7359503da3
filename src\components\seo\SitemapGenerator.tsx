
import React from 'react';

// This component can be used to generate sitemap data
// In a production environment, you'd want to generate this server-side

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export const generateSitemapUrls = (): SitemapUrl[] => {
  const baseUrl = 'https://sa360.co.za';
  const currentDate = new Date().toISOString().split('T')[0];
  
  const staticPages: SitemapUrl[] = [
    {
      loc: `${baseUrl}/`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 1.0
    },
    {
      loc: `${baseUrl}/categories`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.9
    },
    {
      loc: `${baseUrl}/blog`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 0.8
    },
    {
      loc: `${baseUrl}/businesses`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 0.8
    },
    {
      loc: `${baseUrl}/business-match`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.7
    },
    {
      loc: `${baseUrl}/stories`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.6
    },
    {
      loc: `${baseUrl}/special-offers`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 0.7
    },
    {
      loc: `${baseUrl}/pricing`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.5
    }
  ];

  // Category pages
  const categories = [
    'restaurants', 'accommodation', 'retail', 'health', 'automotive',
    'education', 'finance', 'technology', 'professional', 'beauty',
    'construction', 'entertainment'
  ];

  const categoryPages: SitemapUrl[] = categories.map(category => ({
    loc: `${baseUrl}/category/${category}`,
    lastmod: currentDate,
    changefreq: 'weekly',
    priority: 0.7
  }));

  return [...staticPages, ...categoryPages];
};

export const generateSitemapXML = (): string => {
  const urls = generateSitemapUrls();
  
  const urlElements = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
};

// Component for development/testing purposes
const SitemapGenerator: React.FC = () => {
  const handleGenerateSitemap = () => {
    const sitemap = generateSitemapXML();
    const blob = new Blob([sitemap], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sitemap.xml';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-2">Sitemap Generator</h3>
      <button 
        onClick={handleGenerateSitemap}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Download Sitemap.xml
      </button>
    </div>
  );
};

export default SitemapGenerator;
