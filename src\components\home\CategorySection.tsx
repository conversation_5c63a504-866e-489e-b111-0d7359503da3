
import React from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  icon: string | null;
};

const CategorySection = () => {
  const { data: categories, isLoading, error } = useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data as Category[];
    }
  });

  // Default icons for categories that don't have one
  const getDefaultIcon = (slug: string) => {
    const iconMap: { [key: string]: string } = {
      'accommodation': '🏨',
      'arts-entertainment': '🎭',
      'automotive': '🚗',
      'backpackers': '🎒',
      'barber': '✂️',
      'beauty-spa': '💄',
      'bed-breakfast': '🛏️',
      'businesses': '🏢',
      'cafe': '☕',
      'camping': '⛺',
      'casino': '🎰',
      'clinics': '🏥',
      'coffee-shop': '☕',
      'construction': '🔨',
      'farmers-market': '🥬',
      'finance': '💰',
      'flea-market': '🛍️',
      'food': '🍽️',
      'food-market': '🛒',
      'game-drives': '🦁',
      'health-medical': '🏥',
      'hotel': '🏨',
      'real-estate': '🏠',
      'restaurant': '🍽️',
      'shopping': '🛒'
    };
    return iconMap[slug] || '🏢';
  };

  if (isLoading) {
    return (
      <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-800">Explore Categories</h2>
            <p className="mt-2 text-slate-600">Find businesses by category across South Africa</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 text-center animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded mx-auto mb-3"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4 mx-auto"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-800">Explore Categories</h2>
            <p className="mt-2 text-slate-600">Unable to load categories. Please try again later.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-slate-800">Explore Categories</h2>
          <p className="mt-2 text-slate-600">Find businesses by category across South Africa</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {categories?.slice(0, 8).map((category) => (
            <Link
              to={`/category/${category.slug}`}
              key={category.id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 text-center group"
            >
              <div className="text-4xl mb-3">
                {category.icon || getDefaultIcon(category.slug)}
              </div>
              <h3 className="text-lg font-semibold text-slate-800 group-hover:text-[#007749]">
                {category.name}
              </h3>
              <p className="text-sm text-slate-500 mt-1">
                {category.description || 'Business category'}
              </p>
            </Link>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link
            to="/categories"
            className="inline-flex items-center text-[#003087] hover:text-[#007749] font-medium"
          >
            View All Categories
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CategorySection;
