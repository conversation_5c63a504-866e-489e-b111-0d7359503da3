import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Eye, EyeOff, Zap, Star } from 'lucide-react';

interface TierVisibilityIndicatorProps {
  tier: string;
  className?: string;
}

const TierVisibilityIndicator = ({ tier, className }: TierVisibilityIndicatorProps) => {
  const getVisibilityConfig = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return {
          icon: <Zap className="h-3 w-3" />,
          label: 'Maximum Visibility',
          description: 'Top search results, featured placement, sponsored listings',
          className: 'bg-purple-100 text-purple-800 border-purple-200',
        };
      case 'gold':
        return {
          icon: <Star className="h-3 w-3" />,
          label: 'High Visibility',
          description: 'Featured placement, priority in search results',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        };
      case 'silver':
        return {
          icon: <Eye className="h-3 w-3" />,
          label: 'Enhanced Visibility',
          description: 'Priority placement in search results',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
        };
      case 'free':
      default:
        return {
          icon: <EyeOff className="h-3 w-3" />,
          label: 'Standard Visibility',
          description: 'Basic listing in search results',
          className: 'bg-slate-100 text-slate-600 border-slate-200',
        };
    }
  };

  const config = getVisibilityConfig(tier);

  return (
    <div className={`${className}`}>
      <Badge variant="outline" className={`${config.className} flex items-center gap-1`}>
        {config.icon}
        {config.label}
      </Badge>
      <p className="text-xs text-slate-500 mt-1">{config.description}</p>
    </div>
  );
};

export default TierVisibilityIndicator;
