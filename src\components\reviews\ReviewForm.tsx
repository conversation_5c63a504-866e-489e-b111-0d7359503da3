import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Star, Upload, Gift, Award } from 'lucide-react';

interface ReviewFormProps {
  businessId: string;
  businessName: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ReviewForm = ({ businessId, businessName, onSuccess, onCancel }: ReviewFormProps) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [formData, setFormData] = useState({
    review_title: '',
    review_text: '',
    verified_purchase: false,
    recommended: true,
    visit_date: ''
  });
  const [images, setImages] = useState<File[]>([]);
  const [imagePreview, setImagePreview] = useState<string[]>([]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length + images.length > 5) {
      toast.error('Maximum 5 images allowed');
      return;
    }

    const validFiles = files.filter(file => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`${file.name} is too large. Maximum 5MB per image.`);
        return false;
      }
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} is not a valid image file.`);
        return false;
      }
      return true;
    });

    setImages(prev => [...prev, ...validFiles]);

    // Create previews
    validFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreview(prev => prev.filter((_, i) => i !== index));
  };

  const calculatePotentialPoints = () => {
    let points = 10; // Base points
    if (formData.review_text.length > 50) points += 5; // Detailed review
    if (images.length > 0) points += 10; // Photo bonus
    if (formData.verified_purchase) points += 15; // Verified purchase
    return points;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error('Please sign in to submit a review');
      return;
    }

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload images if any
      let imageUrls: string[] = [];
      if (images.length > 0) {
        for (const image of images) {
          const fileExt = image.name.split('.').pop();
          const fileName = `review-${Date.now()}-${Math.random()}.${fileExt}`;
          
          const { data, error } = await supabase.storage
            .from('review-images')
            .upload(fileName, image);

          if (error) throw error;

          const { data: { publicUrl } } = supabase.storage
            .from('review-images')
            .getPublicUrl(fileName);

          imageUrls.push(publicUrl);
        }
      }

      // Submit review
      const { error } = await supabase
        .from('business_reviews')
        .insert({
          business_id: businessId,
          user_id: user.id,
          rating: rating,
          review_title: formData.review_title || null,
          review_text: formData.review_text || null,
          verified_purchase: formData.verified_purchase,
          recommended: formData.recommended,
          visit_date: formData.visit_date || null,
          images: imageUrls.length > 0 ? imageUrls : null,
          status: 'published'
        });

      if (error) throw error;

      toast.success(
        `Review submitted successfully! You earned ${calculatePotentialPoints()} points!`,
        {
          description: 'Thank you for helping other customers make informed decisions.',
        }
      );

      onSuccess?.();
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Star className="h-5 w-5 text-[#007749]" />
          Review {businessName}
        </CardTitle>
        <CardDescription>
          Share your experience and earn reward points
        </CardDescription>
        <div className="flex items-center gap-2">
          <Badge className="bg-[#007749] text-white">
            <Gift className="h-3 w-3 mr-1" />
            Earn {calculatePotentialPoints()} points
          </Badge>
          <Badge variant="outline">
            <Award className="h-3 w-3 mr-1" />
            Help others decide
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div>
            <Label className="text-base font-medium">Overall Rating *</Label>
            <div className="flex items-center gap-1 mt-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoveredRating || rating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
              {rating > 0 && (
                <span className="ml-2 text-sm text-slate-600">
                  {rating === 1 && 'Poor'}
                  {rating === 2 && 'Fair'}
                  {rating === 3 && 'Good'}
                  {rating === 4 && 'Very Good'}
                  {rating === 5 && 'Excellent'}
                </span>
              )}
            </div>
          </div>

          {/* Review Title */}
          <div>
            <Label htmlFor="review_title">Review Title</Label>
            <Input
              id="review_title"
              value={formData.review_title}
              onChange={(e) => setFormData(prev => ({ ...prev, review_title: e.target.value }))}
              placeholder="Summarize your experience in a few words"
              maxLength={100}
            />
          </div>

          {/* Review Text */}
          <div>
            <Label htmlFor="review_text">Your Review</Label>
            <Textarea
              id="review_text"
              value={formData.review_text}
              onChange={(e) => setFormData(prev => ({ ...prev, review_text: e.target.value }))}
              placeholder="Tell others about your experience. What did you like? What could be improved?"
              rows={5}
              maxLength={1000}
            />
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-slate-500">
                {formData.review_text.length > 50 ? (
                  <span className="text-green-600">+5 bonus points for detailed review!</span>
                ) : (
                  'Write 50+ characters for bonus points'
                )}
              </p>
              <span className="text-xs text-slate-500">
                {formData.review_text.length}/1000
              </span>
            </div>
          </div>

          {/* Images */}
          <div>
            <Label>Add Photos (Optional)</Label>
            <div className="mt-2">
              <input
                type="file"
                id="images"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('images')?.click()}
                className="w-full"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Photos (+10 bonus points)
              </Button>
              {imagePreview.length > 0 && (
                <div className="grid grid-cols-3 gap-2 mt-4">
                  {imagePreview.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-20 object-cover rounded border"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Additional Options */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="visit_date">When did you visit?</Label>
              <Input
                id="visit_date"
                type="date"
                value={formData.visit_date}
                onChange={(e) => setFormData(prev => ({ ...prev, visit_date: e.target.value }))}
                max={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="verified_purchase"
                checked={formData.verified_purchase}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, verified_purchase: checked as boolean }))}
              />
              <Label htmlFor="verified_purchase" className="text-sm">
                I can verify this purchase/visit (+15 bonus points)
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="recommended"
                checked={formData.recommended}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, recommended: checked as boolean }))}
              />
              <Label htmlFor="recommended" className="text-sm">
                I would recommend this business to others
              </Label>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-4">
            {onCancel && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button 
              type="submit" 
              className="flex-1 bg-[#007749] hover:bg-[#006739]"
              disabled={isSubmitting || rating === 0}
            >
              {isSubmitting ? 'Submitting...' : `Submit Review & Earn ${calculatePotentialPoints()} Points`}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ReviewForm;
