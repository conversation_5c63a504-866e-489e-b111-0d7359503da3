-- Create customer profiles table
CREATE TABLE public.customer_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT,
  email TEXT,
  phone TEXT,
  location TEXT,
  preferences JSONB DEFAULT '{}', -- Store preferences like budget, categories, etc.
  matching_history JSONB DEFAULT '[]', -- Store past matches and interactions
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create business match questionnaires table
CREATE TABLE public.match_questionnaires (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  questions JSONB NOT NULL, -- Array of question objects
  category TEXT, -- Optional category filter
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create match sessions table
CREATE TABLE public.match_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  customer_profile_id UUID REFERENCES public.customer_profiles(id) ON DELETE SET NULL,
  questionnaire_id UUID REFERENCES public.match_questionnaires(id) ON DELETE CASCADE,
  answers JSONB NOT NULL,
  match_results JSONB, -- Store the matched businesses and scores
  session_data JSONB DEFAULT '{}', -- Additional session metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create match notifications table
CREATE TABLE public.match_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_profile_id UUID REFERENCES public.customer_profiles(id) ON DELETE CASCADE,
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  match_score DECIMAL(5,2) NOT NULL,
  notification_type TEXT DEFAULT 'new_match' CHECK (notification_type IN ('new_match', 'updated_match', 'special_offer')),
  message TEXT,
  is_read BOOLEAN DEFAULT FALSE,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create business matching scores table for caching
CREATE TABLE public.business_match_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  customer_profile_id UUID REFERENCES public.customer_profiles(id) ON DELETE CASCADE,
  match_score DECIMAL(5,2) NOT NULL,
  score_factors JSONB, -- Store what contributed to the score
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(business_id, customer_profile_id)
);

-- Insert default questionnaire
INSERT INTO public.match_questionnaires (title, description, questions, category) VALUES
('General Business Finder', 'Find the perfect business for your needs', 
'[
  {
    "id": 1,
    "question": "What type of business or service are you looking for?",
    "type": "select",
    "options": ["Restaurant", "Accommodation", "Professional Services", "Retail", "Healthcare", "Automotive", "Entertainment", "Beauty & Wellness", "Technology", "Other"],
    "weight": 10
  },
  {
    "id": 2,
    "question": "In which area would you prefer the business to be located?",
    "type": "select",
    "options": ["Cape Town", "Johannesburg", "Durban", "Pretoria", "Port Elizabeth", "Bloemfontein", "East London", "Polokwane", "Nelspruit", "Kimberley", "Any location"],
    "weight": 8
  },
  {
    "id": 3,
    "question": "What is your budget range?",
    "type": "select",
    "options": ["Budget-friendly (Under R500)", "Affordable (R500-R1500)", "Mid-range (R1500-R5000)", "Premium (R5000-R15000)", "Luxury (R15000+)", "No preference"],
    "weight": 7
  },
  {
    "id": 4,
    "question": "How important is it that the business is verified?",
    "type": "select",
    "options": ["Very important", "Somewhat important", "Not important"],
    "weight": 6
  },
  {
    "id": 5,
    "question": "Do you prefer businesses with high ratings?",
    "type": "select",
    "options": ["Yes, only 4+ stars", "Yes, 3+ stars is fine", "Ratings don''t matter much"],
    "weight": 5
  },
  {
    "id": 6,
    "question": "What specific features or services are most important to you?",
    "type": "textarea",
    "placeholder": "Describe any specific requirements, features, or services you need...",
    "weight": 8
  },
  {
    "id": 7,
    "question": "How far are you willing to travel?",
    "type": "select",
    "options": ["Within 5km", "Within 15km", "Within 30km", "Within the city", "Anywhere in the province", "Distance doesn''t matter"],
    "weight": 6
  },
  {
    "id": 8,
    "question": "Do you prefer businesses that offer online services?",
    "type": "select",
    "options": ["Yes, online services preferred", "Online services are a plus", "Prefer in-person services", "No preference"],
    "weight": 4
  }
]', 'general');

-- Create function to calculate match score
CREATE OR REPLACE FUNCTION calculate_business_match_score(
  business_row public.business_listings,
  customer_answers JSONB
) RETURNS DECIMAL AS $$
DECLARE
  score DECIMAL := 0;
  max_score DECIMAL := 0;
  answer_value TEXT;
  business_category TEXT;
  business_location TEXT;
BEGIN
  -- Get business category name
  SELECT name INTO business_category 
  FROM public.categories 
  WHERE id = business_row.category_id;
  
  business_location := business_row.city;
  
  -- Question 1: Business type (weight: 10)
  answer_value := customer_answers->>'1';
  max_score := max_score + 10;
  IF answer_value IS NOT NULL AND business_category ILIKE '%' || answer_value || '%' THEN
    score := score + 10;
  ELSIF answer_value = 'Other' THEN
    score := score + 5; -- Partial match for "Other"
  END IF;
  
  -- Question 2: Location (weight: 8)
  answer_value := customer_answers->>'2';
  max_score := max_score + 8;
  IF answer_value IS NOT NULL THEN
    IF answer_value = 'Any location' THEN
      score := score + 8;
    ELSIF business_location ILIKE '%' || answer_value || '%' THEN
      score := score + 8;
    END IF;
  END IF;
  
  -- Question 3: Budget (weight: 7)
  answer_value := customer_answers->>'3';
  max_score := max_score + 7;
  IF answer_value IS NOT NULL THEN
    IF answer_value = 'No preference' THEN
      score := score + 7;
    ELSIF business_row.subscription_tier IS NOT NULL THEN
      -- Match budget to subscription tier
      IF (answer_value LIKE '%Budget-friendly%' AND business_row.subscription_tier = 'free') OR
         (answer_value LIKE '%Affordable%' AND business_row.subscription_tier IN ('free', 'silver')) OR
         (answer_value LIKE '%Mid-range%' AND business_row.subscription_tier IN ('silver', 'gold')) OR
         (answer_value LIKE '%Premium%' AND business_row.subscription_tier IN ('gold', 'platinum')) OR
         (answer_value LIKE '%Luxury%' AND business_row.subscription_tier = 'platinum') THEN
        score := score + 7;
      ELSE
        score := score + 3; -- Partial match
      END IF;
    END IF;
  END IF;
  
  -- Question 4: Verification importance (weight: 6)
  answer_value := customer_answers->>'4';
  max_score := max_score + 6;
  IF answer_value IS NOT NULL THEN
    IF answer_value = 'Not important' THEN
      score := score + 6;
    ELSIF answer_value = 'Very important' AND business_row.verified = true THEN
      score := score + 6;
    ELSIF answer_value = 'Somewhat important' AND business_row.verified = true THEN
      score := score + 6;
    ELSIF answer_value = 'Somewhat important' AND business_row.verified = false THEN
      score := score + 3;
    END IF;
  END IF;
  
  -- Question 5: Rating preference (weight: 5)
  answer_value := customer_answers->>'5';
  max_score := max_score + 5;
  IF answer_value IS NOT NULL THEN
    IF answer_value = 'Ratings don''t matter much' THEN
      score := score + 5;
    -- Note: We don't have ratings in the current schema, so we'll use featured as a proxy
    ELSIF business_row.featured = true THEN
      score := score + 5;
    ELSE
      score := score + 2; -- Partial score for non-featured businesses
    END IF;
  END IF;
  
  -- Question 6: Specific features (weight: 8)
  answer_value := customer_answers->>'6';
  max_score := max_score + 8;
  IF answer_value IS NOT NULL AND LENGTH(answer_value) > 0 THEN
    -- Simple text matching against business description
    IF business_row.description IS NOT NULL AND 
       (business_row.description ILIKE '%' || answer_value || '%' OR
        answer_value ILIKE '%' || business_row.description || '%') THEN
      score := score + 8;
    ELSE
      score := score + 2; -- Base score for having specific requirements
    END IF;
  ELSE
    score := score + 8; -- Full score if no specific requirements
  END IF;
  
  -- Question 7: Travel distance (weight: 6)
  answer_value := customer_answers->>'7';
  max_score := max_score + 6;
  IF answer_value IS NOT NULL THEN
    IF answer_value = 'Distance doesn''t matter' THEN
      score := score + 6;
    ELSE
      -- For now, give partial score since we don't have exact distance calculation
      score := score + 4;
    END IF;
  END IF;
  
  -- Question 8: Online services (weight: 4)
  answer_value := customer_answers->>'8';
  max_score := max_score + 4;
  IF answer_value IS NOT NULL THEN
    IF answer_value = 'No preference' THEN
      score := score + 4;
    ELSIF business_row.website IS NOT NULL THEN
      score := score + 4; -- Assume businesses with websites offer online services
    ELSE
      score := score + 2; -- Partial score
    END IF;
  END IF;
  
  -- Convert to percentage
  IF max_score > 0 THEN
    RETURN ROUND((score / max_score) * 100, 2);
  ELSE
    RETURN 0;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies
ALTER TABLE public.customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_questionnaires ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_match_scores ENABLE ROW LEVEL SECURITY;

-- Customer profiles policies
CREATE POLICY "Users can manage their own profiles" ON public.customer_profiles
  FOR ALL USING (auth.uid() = user_id);

-- Questionnaires are public
CREATE POLICY "Anyone can view active questionnaires" ON public.match_questionnaires
  FOR SELECT USING (is_active = true);

-- Match sessions policies
CREATE POLICY "Users can manage their own match sessions" ON public.match_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Match notifications policies
CREATE POLICY "Users can view their own notifications" ON public.match_notifications
  FOR SELECT USING (
    customer_profile_id IN (
      SELECT id FROM public.customer_profiles WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own notifications" ON public.match_notifications
  FOR UPDATE USING (
    customer_profile_id IN (
      SELECT id FROM public.customer_profiles WHERE user_id = auth.uid()
    )
  );

-- Business match scores policies
CREATE POLICY "Users can view their own match scores" ON public.business_match_scores
  FOR SELECT USING (
    customer_profile_id IN (
      SELECT id FROM public.customer_profiles WHERE user_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX idx_customer_profiles_user_id ON public.customer_profiles(user_id);
CREATE INDEX idx_match_sessions_user_id ON public.match_sessions(user_id);
CREATE INDEX idx_match_sessions_created_at ON public.match_sessions(created_at);
CREATE INDEX idx_match_notifications_customer_profile_id ON public.match_notifications(customer_profile_id);
CREATE INDEX idx_match_notifications_is_read ON public.match_notifications(is_read);
CREATE INDEX idx_business_match_scores_business_id ON public.business_match_scores(business_id);
CREATE INDEX idx_business_match_scores_customer_profile_id ON public.business_match_scores(customer_profile_id);
