import { useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface EmailNotification {
  id: string;
  type: string;
  recipient_email: string;
  subject: string;
  data: any;
  status: 'pending' | 'sent' | 'failed';
  attempts: number;
  created_at: string;
  updated_at: string;
}

export const useEmailNotifications = (autoProcess = false, intervalMs = 30000) => {
  // Function to manually trigger email processing
  const processEmailNotifications = useCallback(async () => {
    try {
      const { data, error } = await supabase.functions.invoke('process-email-notifications');
      
      if (error) {
        console.error('Error processing email notifications:', error);
        toast.error('Failed to process email notifications');
        return { success: false, error };
      }

      console.log('Email processing result:', data);
      
      if (data.processed > 0) {
        toast.success(`Processed ${data.processed} email notifications`);
      }
      
      return { success: true, data };
    } catch (error) {
      console.error('Error calling email processor:', error);
      toast.error('Failed to process email notifications');
      return { success: false, error };
    }
  }, []);

  // Function to get pending email notifications count
  const getPendingNotificationsCount = useCallback(async () => {
    try {
      const { count, error } = await supabase
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      if (error) {
        console.error('Error fetching pending notifications count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error fetching pending notifications count:', error);
      return 0;
    }
  }, []);

  // Function to get email notifications with pagination
  const getEmailNotifications = useCallback(async (
    page = 0, 
    limit = 20, 
    status?: 'pending' | 'sent' | 'failed'
  ) => {
    try {
      let query = supabase
        .from('email_notifications')
        .select('*')
        .order('created_at', { ascending: false })
        .range(page * limit, (page + 1) * limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching email notifications:', error);
        return { data: [], error };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching email notifications:', error);
      return { data: [], error };
    }
  }, []);

  // Function to retry failed email notifications
  const retryFailedNotifications = useCallback(async () => {
    try {
      // Reset failed notifications to pending status
      const { error } = await supabase
        .from('email_notifications')
        .update({ 
          status: 'pending', 
          attempts: 0,
          error_message: null,
          updated_at: new Date().toISOString()
        })
        .eq('status', 'failed');

      if (error) {
        console.error('Error retrying failed notifications:', error);
        toast.error('Failed to retry notifications');
        return { success: false, error };
      }

      toast.success('Failed notifications have been queued for retry');
      
      // Process them immediately
      return await processEmailNotifications();
    } catch (error) {
      console.error('Error retrying failed notifications:', error);
      toast.error('Failed to retry notifications');
      return { success: false, error };
    }
  }, [processEmailNotifications]);

  // Auto-process emails at regular intervals if enabled
  useEffect(() => {
    if (!autoProcess) return;

    const interval = setInterval(async () => {
      const pendingCount = await getPendingNotificationsCount();
      if (pendingCount > 0) {
        console.log(`Processing ${pendingCount} pending email notifications...`);
        await processEmailNotifications();
      }
    }, intervalMs);

    return () => clearInterval(interval);
  }, [autoProcess, intervalMs, processEmailNotifications, getPendingNotificationsCount]);

  // Listen for new email notifications in real-time
  useEffect(() => {
    const channel = supabase
      .channel('email-notifications-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'email_notifications',
        },
        (payload) => {
          const newNotification = payload.new as EmailNotification;
          console.log('New email notification created:', newNotification);
          
          // Optionally show a toast for new notifications
          if (newNotification.type === 'listing_approval') {
            toast.info('New listing approval email queued');
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return {
    processEmailNotifications,
    getPendingNotificationsCount,
    getEmailNotifications,
    retryFailedNotifications,
  };
};

// Utility function to manually trigger listing approval email
export const triggerListingApprovalEmail = async (listingId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('send-listing-approval-email', {
      body: { listingId }
    });

    if (error) {
      console.error('Error sending listing approval email:', error);
      toast.error('Failed to send approval email');
      return { success: false, error };
    }

    toast.success('Approval email sent successfully');
    return { success: true, data };
  } catch (error) {
    console.error('Error sending listing approval email:', error);
    toast.error('Failed to send approval email');
    return { success: false, error };
  }
};

// Hook for admin dashboard to monitor email notifications
export const useEmailNotificationStats = () => {
  const { getEmailNotifications, getPendingNotificationsCount } = useEmailNotifications();

  const getStats = useCallback(async () => {
    try {
      const [pendingCount, sentResult, failedResult] = await Promise.all([
        getPendingNotificationsCount(),
        getEmailNotifications(0, 1, 'sent'),
        getEmailNotifications(0, 1, 'failed')
      ]);

      // Get total counts for each status
      const { count: sentCount } = await supabase
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'sent');

      const { count: failedCount } = await supabase
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'failed');

      return {
        pending: pendingCount,
        sent: sentCount || 0,
        failed: failedCount || 0,
        total: pendingCount + (sentCount || 0) + (failedCount || 0)
      };
    } catch (error) {
      console.error('Error fetching email notification stats:', error);
      return {
        pending: 0,
        sent: 0,
        failed: 0,
        total: 0
      };
    }
  }, [getEmailNotifications, getPendingNotificationsCount]);

  return { getStats };
};
