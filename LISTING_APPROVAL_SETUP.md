# Listing Approval Email Notification System

This document describes the automated email notification system for listing approvals that was implemented for SA Business Beacon.

## Overview

The system automatically sends email notifications to `<EMAIL>` when new business listings are submitted with "pending" status. The emails include approval/rejection links for quick action.

## Components

### 1. Database Tables

- **`listing_approval_tokens`**: Stores secure tokens for approval links
- **`email_notifications`**: Queue for email notifications to be processed

### 2. Edge Functions

- **`send-listing-approval-email`**: Sends individual approval emails (legacy, kept for manual triggers)
- **`approve-listing`**: Handles approval/rejection via email links
- **`process-email-notifications`**: Processes queued email notifications

### 3. Database Trigger

- **`trigger_send_listing_approval_email`**: Automatically queues email notifications when listings are submitted

### 4. React Components

- **`useEmailNotifications`**: Hook for managing email notifications
- **`EmailNotificationManager`**: Admin component for monitoring email system

## Setup Instructions

### 1. Environment Variables

Add the following environment variables to your Supabase project:

```bash
# Resend API Key for sending emails
RESEND_API_KEY=your_resend_api_key_here

# Your Supabase URL (usually auto-configured)
SUPABASE_URL=https://your-project.supabase.co

# Service role key (usually auto-configured)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 2. Deploy Database Migration

Run the migration to create the necessary tables and triggers:

```bash
supabase db push
```

This will create:
- `listing_approval_tokens` table
- `email_notifications` table
- Database trigger for automatic email queuing
- Required functions and policies

### 3. Deploy Edge Functions

Deploy the edge functions to Supabase:

```bash
# Deploy all functions
supabase functions deploy

# Or deploy individually
supabase functions deploy send-listing-approval-email
supabase functions deploy approve-listing
supabase functions deploy process-email-notifications
```

### 4. Configure Resend

1. Sign up for a Resend account at https://resend.com
2. Verify your domain (sa360.co.za) in Resend
3. Create an API key
4. Add the API key to your Supabase environment variables

### 5. Set up Email Processing

The system uses a queue-based approach. You have two options:

#### Option A: Manual Processing (Recommended for testing)
Use the admin dashboard to manually process emails:
- Import `EmailNotificationManager` component
- Add it to your admin dashboard
- Use the "Process Emails" button to send queued notifications

#### Option B: Automated Processing
Set up a cron job or scheduled function to call the email processor:

```bash
# Example: Call every 5 minutes
curl -X POST "https://your-project.supabase.co/functions/v1/process-email-notifications" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY"
```

## Testing the System

### 1. Test Listing Submission

1. Submit a new business listing through the normal form
2. Check that the listing has `status = 'pending'`
3. Verify that an email notification was queued:

```sql
SELECT * FROM email_notifications WHERE type = 'listing_approval' ORDER BY created_at DESC LIMIT 5;
```

### 2. Test Email Processing

1. Use the admin dashboard or call the processor manually:

```bash
curl -X POST "https://your-project.supabase.co/functions/v1/process-email-notifications" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY"
```

2. Check that the email was sent (status should change to 'sent')
3. Check your <NAME_EMAIL> for the approval email

### 3. Test Approval Links

1. Click the "Approve" or "Reject" button in the email
2. Verify that you see a success page
3. Check that the listing status was updated in the database:

```sql
SELECT id, title, status, updated_at FROM business_listings WHERE status IN ('approved', 'rejected') ORDER BY updated_at DESC LIMIT 5;
```

### 4. Test Token Security

1. Try using an expired or invalid token
2. Verify that appropriate error messages are shown
3. Try using the same token twice (should fail the second time)

## Monitoring and Maintenance

### Admin Dashboard

The `EmailNotificationManager` component provides:
- Real-time stats (pending, sent, failed emails)
- Manual email processing
- Retry failed notifications
- View recent notification history

### Database Queries

Monitor the system with these queries:

```sql
-- Check pending notifications
SELECT COUNT(*) as pending_count FROM email_notifications WHERE status = 'pending';

-- Check failed notifications
SELECT * FROM email_notifications WHERE status = 'failed';

-- Check recent approvals
SELECT bl.title, bl.status, bl.updated_at 
FROM business_listings bl 
WHERE bl.status IN ('approved', 'rejected') 
ORDER BY bl.updated_at DESC 
LIMIT 10;

-- Clean up expired tokens (run periodically)
SELECT cleanup_expired_approval_tokens();
```

### Troubleshooting

**Emails not being sent:**
1. Check Resend API key is correct
2. Verify domain is verified in Resend
3. Check email notification queue for errors
4. Ensure edge functions are deployed

**Approval links not working:**
1. Check that tokens are being generated correctly
2. Verify the approve-listing function is deployed
3. Check token expiration (7 days by default)

**Database trigger not firing:**
1. Verify the trigger exists: `\d+ business_listings` in psql
2. Check that new listings have status = 'pending'
3. Look for trigger errors in Supabase logs

## Customization

### Email Template

Modify the email template in `process-email-notifications/index.ts` in the `generateApprovalEmailHtml` function.

### Approval Token Expiration

Change the token expiration time in the edge functions (currently set to 7 days).

### Email Recipients

Update the recipient email address in the edge functions (currently `<EMAIL>`).

### Processing Frequency

Adjust how often emails are processed by modifying the cron job or manual processing frequency.

## Security Considerations

- Approval tokens expire after 7 days
- Tokens are single-use (deleted after use)
- Only service role can access approval tokens table
- Email links include action verification
- Failed attempts are logged and limited

## Future Enhancements

- Add email templates for different notification types
- Implement email preferences for different admin users
- Add webhook support for real-time processing
- Create dashboard for approval statistics
- Add email delivery status tracking
