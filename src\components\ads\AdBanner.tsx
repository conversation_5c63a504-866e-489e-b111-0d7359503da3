import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Eye, MousePointer } from 'lucide-react';
import useAdTracking from '@/hooks/useAdTracking';

interface Ad {
  id: string;
  title: string;
  description: string | null;
  image_url: string | null;
  target_url: string;
  ad_type: string;
  position: string;
  clicks: number;
  impressions: number;
  status: string;
  business_id: string | null;
}

interface AdBannerProps {
  position: 'top' | 'middle' | 'bottom' | 'sidebar';
  pageType?: string;
  className?: string;
}

const AdBanner = ({ position, pageType = 'homepage', className }: AdBannerProps) => {
  const { user } = useAuth();
  const { trackImpression, trackClick } = useAdTracking();

  const { data: ads, isLoading, refetch } = useQuery({
    queryKey: ['ads', position, pageType],
    queryFn: async () => {
      const { data, error } = await supabase
        .rpc('get_ads_for_placement', {
          position_param: position,
          page_type_param: pageType,
          device_type_param: 'desktop', // Will be determined by tracking hook
          limit_param: 3
        });

      if (error) throw error;
      return data as Ad[];
    },
    refetchInterval: 30000, // Refresh every 30 seconds for rotation
  });

  const handleAdClick = async (ad: Ad) => {
    await trackClick({
      adId: ad.id,
      eventType: 'click',
      pageUrl: window.location.href,
      referrer: document.referrer
    });

    // Open the target URL
    if (ad.target_url) {
      window.open(ad.target_url, '_blank');
    }
  };

  useEffect(() => {
    // Track impressions for all visible ads after a delay
    if (ads && ads.length > 0) {
      const timers = ads.map(ad =>
        setTimeout(() => {
          trackImpression({
            adId: ad.id,
            eventType: 'impression',
            viewDuration: 1,
            pageUrl: window.location.href
          });
        }, 1000) // Track after 1 second of viewing
      );

      return () => {
        timers.forEach(timer => clearTimeout(timer));
      };
    }
  }, [ads, trackImpression]);

  if (isLoading || !ads || ads.length === 0) {
    return null;
  }

  const getAdLayout = () => {
    switch (position) {
      case 'top':
      case 'bottom':
        return 'horizontal';
      case 'sidebar':
        return 'vertical';
      case 'middle':
        return 'grid';
      default:
        return 'horizontal';
    }
  };

  const layout = getAdLayout();

  return (
    <div className={`ad-banner ad-banner-${position} ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <Badge variant="outline" className="text-xs text-slate-500">
          Sponsored
        </Badge>
      </div>
      
      {layout === 'horizontal' && (
        <div className="flex gap-4 overflow-x-auto">
          {ads.map((ad) => (
            <Card 
              key={ad.id}
              className="flex-shrink-0 w-80 cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleAdClick(ad)}
            >
              <div className="p-4">
                {ad.image_url && (
                  <div className="aspect-[16/9] mb-3 overflow-hidden rounded">
                    <img 
                      src={ad.image_url} 
                      alt={ad.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm mb-1">{ad.title}</h3>
                    {ad.description && (
                      <p className="text-xs text-slate-600 line-clamp-2">{ad.description}</p>
                    )}
                  </div>
                  <ExternalLink className="h-4 w-4 text-slate-400 ml-2 flex-shrink-0" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {layout === 'vertical' && (
        <div className="space-y-4">
          {ads.map((ad) => (
            <Card 
              key={ad.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleAdClick(ad)}
            >
              <div className="p-3">
                {ad.image_url && (
                  <div className="aspect-square mb-2 overflow-hidden rounded">
                    <img 
                      src={ad.image_url} 
                      alt={ad.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm mb-1">{ad.title}</h3>
                    {ad.description && (
                      <p className="text-xs text-slate-600 line-clamp-3">{ad.description}</p>
                    )}
                  </div>
                  <ExternalLink className="h-3 w-3 text-slate-400 ml-2 flex-shrink-0" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {layout === 'grid' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {ads.map((ad) => (
            <Card 
              key={ad.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleAdClick(ad)}
            >
              <div className="p-4">
                {ad.image_url && (
                  <div className="aspect-[16/9] mb-3 overflow-hidden rounded">
                    <img 
                      src={ad.image_url} 
                      alt={ad.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm mb-1">{ad.title}</h3>
                    {ad.description && (
                      <p className="text-xs text-slate-600 line-clamp-2">{ad.description}</p>
                    )}
                  </div>
                  <ExternalLink className="h-4 w-4 text-slate-400 ml-2 flex-shrink-0" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdBanner;
