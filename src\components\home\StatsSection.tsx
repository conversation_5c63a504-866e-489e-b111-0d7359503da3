
import React from "react";

const stats = [
  { id: 1, name: "Businesses Listed", value: "10,000+" },
  { id: 2, name: "Monthly Visitors", value: "50,000+" },
  { id: 3, name: "Customer Reviews", value: "25,000+" },
  { id: 4, name: "SA Provinces Covered", value: "9" },
];

const StatsSection = () => {
  return (
    <section className="bg-[#003087] py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white">
            South Africa's Leading Business Directory
          </h2>
          <p className="mt-2 text-white/80">
            Connecting South Africans with businesses nationwide
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat) => (
            <div key={stat.id} className="text-center">
              <p className="text-3xl md:text-4xl font-bold text-[#FDB913]">{stat.value}</p>
              <p className="text-white/80 mt-1">{stat.name}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
