
import React from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Layout from "@/components/layout/Layout";
import SEOHead from "@/components/seo/SEOHead";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, ArrowLeft, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const BlogArticle = () => {
  const { slug } = useParams();

  const { data: article, isLoading } = useQuery({
    queryKey: ['blog-article', slug],
    queryFn: async () => {
      if (!slug) throw new Error('No slug provided');
      
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!slug,
  });

  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!article) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold text-slate-800 mb-4">Article Not Found</h1>
          <p className="text-slate-600 mb-6">The article you're looking for doesn't exist or has been removed.</p>
          <Link to="/blog">
            <Button>Back to Blog</Button>
          </Link>
        </div>
      </Layout>
    );
  }

  const publishedDate = article.published_at ? new Date(article.published_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : '';

  const readingTime = article.content ? Math.ceil(article.content.length / 1000) : 5;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": article.title,
    "description": article.excerpt || "",
    "image": article.featured_image_url || "https://sa360.co.za/lovable-uploads/b1ef264a-**************-253c678eb9d9.png",
    "author": {
      "@type": "Person",
      "name": article.author_name || "SA360 Team"
    },
    "datePublished": article.published_at,
    "dateModified": article.updated_at,
    "publisher": {
      "@type": "Organization",
      "name": "SA360",
      "logo": {
        "@type": "ImageObject",
        "url": "https://sa360.co.za/lovable-uploads/b1ef264a-**************-253c678eb9d9.png"
      }
    }
  };

  return (
    <>
      <SEOHead
        title={article.title}
        description={article.excerpt || `Read ${article.title} on SA360 - South Africa's premier business directory and travel guide.`}
        keywords="South Africa, travel, business, guide, tips, destinations"
        ogImage={article.featured_image_url}
        ogType="article"
        structuredData={structuredData}
      />
      <Layout>
        <article className="py-8">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <nav className="mb-6">
              <Link to="/blog" className="inline-flex items-center text-[#003087] hover:text-[#00246a]">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Link>
            </nav>

            {/* Article Header */}
            <header className="mb-8">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-800 mb-4 leading-tight">
                {article.title}
              </h1>
              
              {article.excerpt && (
                <p className="text-xl text-slate-600 mb-6 leading-relaxed">
                  {article.excerpt}
                </p>
              )}

              <div className="flex flex-wrap items-center gap-4 text-sm text-slate-500 mb-6">
                {article.published_at && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <time dateTime={article.published_at}>{publishedDate}</time>
                  </div>
                )}
                {article.author_name && (
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    <span>{article.author_name}</span>
                  </div>
                )}
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{readingTime} min read</span>
                </div>
              </div>
            </header>

            {/* Featured Image */}
            {article.featured_image_url && (
              <div className="mb-8">
                <img
                  src={article.featured_image_url}
                  alt={article.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                />
              </div>
            )}

            {/* Article Content */}
            <div className="max-w-4xl mx-auto">
              <div className="prose prose-lg max-w-none">
                {article.content ? (
                  <div dangerouslySetInnerHTML={{ __html: article.content }} />
                ) : (
                  <p className="text-slate-600">Content coming soon...</p>
                )}
              </div>

              {/* Author Info */}
              {article.author_name && (
                <div className="mt-12 p-6 bg-slate-50 rounded-lg">
                  <h3 className="text-lg font-semibold text-slate-800 mb-2">About the Author</h3>
                  <p className="text-slate-600">
                    <strong>{article.author_name}</strong> is a contributor to SA360, sharing insights about South African travel, business, and local culture.
                  </p>
                </div>
              )}

              {/* Call to Action */}
              <div className="mt-12 text-center p-8 bg-[#003087] text-white rounded-lg">
                <h3 className="text-2xl font-bold mb-4">Explore More of South Africa</h3>
                <p className="mb-6">Discover amazing businesses, destinations, and opportunities across the Rainbow Nation.</p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link to="/businesses">
                    <Button variant="outline" className="bg-white text-[#003087] hover:bg-slate-100">
                      Browse Businesses
                    </Button>
                  </Link>
                  <Link to="/blog">
                    <Button variant="outline" className="bg-white text-[#003087] hover:bg-slate-100">
                      Read More Articles
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </article>
      </Layout>
    </>
  );
};

export default BlogArticle;
