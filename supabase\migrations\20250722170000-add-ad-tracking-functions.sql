-- <PERSON><PERSON> functions for tracking ad clicks and impressions

-- Function to increment ad impressions
CREATE OR REPLACE FUNCTION increment_ad_impressions(ad_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.ads 
  SET impressions = impressions + 1,
      updated_at = NOW()
  WHERE id = ad_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment ad clicks
CREATE OR REPLACE FUNCTION increment_ad_clicks(ad_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.ads 
  SET clicks = clicks + 1,
      updated_at = NOW()
  WHERE id = ad_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create ad_clicks table for detailed click tracking
CREATE TABLE public.ad_clicks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_id UUID REFERENCES public.ads(id) ON DELETE CASCADE,
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ad_impressions table for detailed impression tracking
CREATE TABLE public.ad_impressions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_id UUID REFERENCES public.ads(id) ON DELETE CASCADE,
  user_agent TEXT,
  ip_address INET,
  page_url TEXT,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for ad tracking tables
ALTER TABLE public.ad_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ad_impressions ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert click/impression data (for tracking)
CREATE POLICY "Anyone can track ad clicks" ON public.ad_clicks
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can track ad impressions" ON public.ad_impressions
  FOR INSERT WITH CHECK (true);

-- Only ad owners can view their tracking data
CREATE POLICY "Ad owners can view their click data" ON public.ad_clicks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.ads 
      WHERE ads.id = ad_clicks.ad_id 
      AND ads.business_id IN (
        SELECT id FROM public.business_listings 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Ad owners can view their impression data" ON public.ad_impressions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.ads 
      WHERE ads.id = ad_impressions.ad_id 
      AND ads.business_id IN (
        SELECT id FROM public.business_listings 
        WHERE user_id = auth.uid()
      )
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_ad_clicks_ad_id ON public.ad_clicks(ad_id);
CREATE INDEX idx_ad_clicks_clicked_at ON public.ad_clicks(clicked_at);
CREATE INDEX idx_ad_impressions_ad_id ON public.ad_impressions(ad_id);
CREATE INDEX idx_ad_impressions_viewed_at ON public.ad_impressions(viewed_at);

-- Add RLS policies for ads table
ALTER TABLE public.ads ENABLE ROW LEVEL SECURITY;

-- Anyone can view active ads
CREATE POLICY "Anyone can view active ads" ON public.ads
  FOR SELECT USING (status = 'active');

-- Users can create ads
CREATE POLICY "Users can create ads" ON public.ads
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Users can update their own ads
CREATE POLICY "Users can update their own ads" ON public.ads
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Users can view their own ads
CREATE POLICY "Users can view their own ads" ON public.ads
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );
