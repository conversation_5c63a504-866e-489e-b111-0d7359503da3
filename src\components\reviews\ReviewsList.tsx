import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Star, 
  ThumbsUp, 
  ThumbsDown, 
  MessageSquare, 
  Calendar,
  CheckCircle,
  Camera,
  Award
} from 'lucide-react';
import { toast } from 'sonner';

interface Review {
  id: string;
  rating: number;
  review_title: string | null;
  review_text: string | null;
  verified_purchase: boolean;
  helpful_votes: number;
  total_votes: number;
  response_from_business: string | null;
  response_date: string | null;
  images: string[] | null;
  visit_date: string | null;
  recommended: boolean | null;
  created_at: string;
  user_id: string;
}

interface ReviewsListProps {
  businessId: string;
  businessOwnerId?: string;
}

const ReviewsList = ({ businessId, businessOwnerId }: ReviewsListProps) => {
  const { user } = useAuth();
  const [responseText, setResponseText] = useState<Record<string, string>>({});
  const [isResponding, setIsResponding] = useState<Record<string, boolean>>({});

  const { data: reviews, isLoading, refetch } = useQuery({
    queryKey: ['business-reviews', businessId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('business_reviews')
        .select('*')
        .eq('business_id', businessId)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Review[];
    },
  });

  const { data: reputation } = useQuery({
    queryKey: ['business-reputation', businessId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('business_reputation')
        .select('*')
        .eq('business_id', businessId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    },
  });

  const voteOnReview = async (reviewId: string, voteType: 'helpful' | 'not_helpful') => {
    if (!user) {
      toast.error('Please sign in to vote on reviews');
      return;
    }

    try {
      const { error } = await supabase
        .from('review_votes')
        .insert({
          review_id: reviewId,
          user_id: user.id,
          vote_type: voteType
        });

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          toast.error('You have already voted on this review');
        } else {
          throw error;
        }
        return;
      }

      // Update the review's helpful votes count
      const currentReview = reviews?.find(r => r.id === reviewId);
      if (currentReview && voteType === 'helpful') {
        await supabase
          .from('business_reviews')
          .update({
            helpful_votes: currentReview.helpful_votes + 1,
            total_votes: currentReview.total_votes + 1
          })
          .eq('id', reviewId);
      } else if (currentReview) {
        await supabase
          .from('business_reviews')
          .update({
            total_votes: currentReview.total_votes + 1
          })
          .eq('id', reviewId);
      }

      toast.success('Thank you for your feedback!');
      refetch();
    } catch (error) {
      console.error('Error voting on review:', error);
      toast.error('Failed to submit vote');
    }
  };

  const respondToReview = async (reviewId: string) => {
    if (!user || user.id !== businessOwnerId) {
      toast.error('Only business owners can respond to reviews');
      return;
    }

    const response = responseText[reviewId]?.trim();
    if (!response) {
      toast.error('Please enter a response');
      return;
    }

    setIsResponding(prev => ({ ...prev, [reviewId]: true }));

    try {
      const { error } = await supabase
        .from('business_reviews')
        .update({
          response_from_business: response,
          response_date: new Date().toISOString()
        })
        .eq('id', reviewId);

      if (error) throw error;

      toast.success('Response posted successfully!');
      setResponseText(prev => ({ ...prev, [reviewId]: '' }));
      refetch();
    } catch (error) {
      console.error('Error responding to review:', error);
      toast.error('Failed to post response');
    } finally {
      setIsResponding(prev => ({ ...prev, [reviewId]: false }));
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-32"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Reputation Summary */}
      {reputation && (
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-[#007749]">
                  {reputation.average_rating.toFixed(1)}
                </div>
                <div className="flex justify-center mb-1">
                  {renderStars(Math.round(reputation.average_rating))}
                </div>
                <div className="text-sm text-slate-600">
                  {reputation.total_reviews} reviews
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {reputation.recommended_percentage.toFixed(0)}%
                </div>
                <div className="text-sm text-slate-600">Recommended</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {reputation.verified_reviews}
                </div>
                <div className="text-sm text-slate-600">Verified Reviews</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {reputation.response_rate.toFixed(0)}%
                </div>
                <div className="text-sm text-slate-600">Response Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      {!reviews || reviews.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageSquare className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 mb-2">No reviews yet</p>
            <p className="text-sm text-slate-500">
              Be the first to share your experience and earn reward points!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {renderStars(review.rating)}
                    <div className="flex items-center gap-2">
                      {review.verified_purchase && (
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Verified
                        </Badge>
                      )}
                      {review.images && review.images.length > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <Camera className="h-3 w-3 mr-1" />
                          {review.images.length} photo{review.images.length > 1 ? 's' : ''}
                        </Badge>
                      )}
                      {review.recommended && (
                        <Badge className="bg-[#007749] text-white text-xs">
                          <Award className="h-3 w-3 mr-1" />
                          Recommended
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-sm text-slate-500 flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {new Date(review.created_at).toLocaleDateString()}
                  </div>
                </div>

                {review.review_title && (
                  <h3 className="font-semibold text-lg mb-2">{review.review_title}</h3>
                )}

                {review.review_text && (
                  <p className="text-slate-700 mb-4">{review.review_text}</p>
                )}

                {review.images && review.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
                    {review.images.map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`Review image ${index + 1}`}
                        className="w-full h-24 object-cover rounded border cursor-pointer hover:opacity-80"
                        onClick={() => window.open(image, '_blank')}
                      />
                    ))}
                  </div>
                )}

                {review.visit_date && (
                  <p className="text-sm text-slate-500 mb-4">
                    Visited on {new Date(review.visit_date).toLocaleDateString()}
                  </p>
                )}

                {/* Business Response */}
                {review.response_from_business && (
                  <div className="bg-slate-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <MessageSquare className="h-4 w-4 text-[#007749]" />
                      <span className="font-medium text-[#007749]">Business Response</span>
                      {review.response_date && (
                        <span className="text-xs text-slate-500">
                          {new Date(review.response_date).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    <p className="text-slate-700">{review.response_from_business}</p>
                  </div>
                )}

                {/* Business Owner Response Form */}
                {user && user.id === businessOwnerId && !review.response_from_business && (
                  <div className="border-t pt-4 mt-4">
                    <div className="space-y-3">
                      <Textarea
                        placeholder="Respond to this review..."
                        value={responseText[review.id] || ''}
                        onChange={(e) => setResponseText(prev => ({ ...prev, [review.id]: e.target.value }))}
                        rows={3}
                      />
                      <Button
                        onClick={() => respondToReview(review.id)}
                        disabled={isResponding[review.id]}
                        className="bg-[#007749] hover:bg-[#006739]"
                        size="sm"
                      >
                        {isResponding[review.id] ? 'Posting...' : 'Post Response'}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Helpful Votes */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => voteOnReview(review.id, 'helpful')}
                      className="text-slate-600 hover:text-green-600"
                    >
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      Helpful ({review.helpful_votes})
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => voteOnReview(review.id, 'not_helpful')}
                      className="text-slate-600 hover:text-red-600"
                    >
                      <ThumbsDown className="h-4 w-4 mr-1" />
                      Not Helpful
                    </Button>
                  </div>
                  
                  {review.total_votes > 0 && (
                    <span className="text-sm text-slate-500">
                      {review.helpful_votes} of {review.total_votes} found this helpful
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewsList;
