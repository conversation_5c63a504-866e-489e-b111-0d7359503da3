import React, { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Shield, CreditCard, Settings, TrendingUp } from 'lucide-react';

interface AffiliateLink {
  id: string;
  title: string;
  description: string | null;
  affiliate_url: string;
  category: string;
  priority: number;
  affiliate_partners: {
    name: string;
    logo_url: string | null;
  };
}

interface AffiliateLinksProps {
  context: 'business_listing' | 'category_page' | 'homepage';
  category?: string;
  businessId?: string;
  className?: string;
}

const AffiliateLinks = ({ context, category, businessId, className }: AffiliateLinksProps) => {
  const { data: affiliateLinks, isLoading } = useQuery({
    queryKey: ['affiliate-links', context, category],
    queryFn: async () => {
      let query = supabase
        .from('affiliate_links')
        .select(`
          *,
          affiliate_partners (
            name,
            logo_url
          )
        `)
        .eq('status', 'active')
        .eq('placement_context', context)
        .order('priority', { ascending: false })
        .limit(4);

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as AffiliateLink[];
    },
  });

  const trackClick = async (linkId: string) => {
    try {
      await supabase
        .from('affiliate_clicks')
        .insert({
          link_id: linkId,
          business_id: businessId || null,
          user_agent: navigator.userAgent,
          referrer: document.referrer || null
        });
    } catch (error) {
      console.error('Error tracking affiliate click:', error);
    }
  };

  const handleLinkClick = (link: AffiliateLink) => {
    trackClick(link.id);
    window.open(link.affiliate_url, '_blank', 'noopener,noreferrer');
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'banking':
        return <CreditCard className="h-4 w-4" />;
      case 'insurance':
        return <Shield className="h-4 w-4" />;
      case 'business_services':
        return <Settings className="h-4 w-4" />;
      case 'marketing':
        return <TrendingUp className="h-4 w-4" />;
      default:
        return <ExternalLink className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'banking':
        return 'bg-blue-100 text-blue-800';
      case 'insurance':
        return 'bg-green-100 text-green-800';
      case 'business_services':
        return 'bg-purple-100 text-purple-800';
      case 'marketing':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading || !affiliateLinks || affiliateLinks.length === 0) {
    return null;
  }

  return (
    <div className={`affiliate-links ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-slate-800">Recommended Services</h3>
        <Badge variant="outline" className="text-xs">
          Partner Recommendations
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {affiliateLinks.map((link) => (
          <Card 
            key={link.id}
            className="cursor-pointer hover:shadow-md transition-all duration-200 border-l-4 border-l-[#007749]"
            onClick={() => handleLinkClick(link)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  {link.affiliate_partners.logo_url ? (
                    <img 
                      src={link.affiliate_partners.logo_url} 
                      alt={link.affiliate_partners.name}
                      className="w-8 h-8 object-contain"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                      {getCategoryIcon(link.category)}
                    </div>
                  )}
                  <Badge className={getCategoryColor(link.category)}>
                    {getCategoryIcon(link.category)}
                    <span className="ml-1 capitalize">{link.category.replace('_', ' ')}</span>
                  </Badge>
                </div>
                <ExternalLink className="h-4 w-4 text-slate-400" />
              </div>

              <div>
                <h4 className="font-semibold text-slate-800 mb-1">{link.title}</h4>
                <p className="text-sm text-slate-600 mb-2">{link.description}</p>
                <p className="text-xs text-slate-500">by {link.affiliate_partners.name}</p>
              </div>

              <div className="mt-3 pt-3 border-t border-slate-100">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full text-[#007749] border-[#007749] hover:bg-[#007749] hover:text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLinkClick(link);
                  }}
                >
                  Learn More
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-4 text-center">
        <p className="text-xs text-slate-500">
          These are partner recommendations. SA360 may earn a commission from qualifying purchases.
        </p>
      </div>
    </div>
  );
};

export default AffiliateLinks;
