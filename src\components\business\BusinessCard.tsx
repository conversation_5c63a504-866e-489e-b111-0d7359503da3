
import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Phone, Globe, Verified } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PremiumBadge from './PremiumBadge';
import { BusinessListing } from '@/hooks/useBusinessListings';

interface BusinessCardProps {
  business: BusinessListing;
  featured?: boolean;
}

const BusinessCard = ({ business, featured = false }: BusinessCardProps) => {
  const isPremium = business.subscription_tier && business.subscription_tier !== 'free';
  const hasMultipleImages = business.images && business.images.length > 1;
  
  return (
    <Card className={`overflow-hidden transition-all duration-300 hover:shadow-lg ${
      isPremium ? 'ring-2 ring-[#FDB913] ring-opacity-50' : ''
    } ${featured ? 'border-[#007749] shadow-md' : ''}`}>
      <div className="aspect-[16/9] relative">
        <img 
          src={business.images?.[0] || business.logo_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80'} 
          alt={business.title} 
          className="w-full h-full object-cover"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          {business.subscription_tier && business.subscription_tier !== 'free' && (
            <PremiumBadge tier={business.subscription_tier} />
          )}
          {business.featured && (
            <Badge className="bg-[#FDB913] text-black">
              Featured
            </Badge>
          )}
          {business.verified && (
            <Badge className="bg-green-600 text-white flex items-center gap-1">
              <Verified className="h-3 w-3" />
              Verified
            </Badge>
          )}
        </div>
        {hasMultipleImages && (
          <div className="absolute bottom-2 right-2">
            <Badge variant="secondary" className="text-xs">
              +{business.images!.length - 1} photos
            </Badge>
          </div>
        )}
      </div>
      
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <Link to={`/business/${business.id}`} className="flex-1">
            <h3 className={`font-semibold text-lg text-slate-800 hover:text-[#007749] transition-colors ${
              isPremium ? 'text-[#003087]' : ''
            }`}>
              {business.title}
            </h3>
          </Link>
        </div>
        
        <div className="space-y-2 mb-3">
          {business.categories && (
            <p className="text-sm text-slate-500">
              {business.categories.name}
            </p>
          )}
          
          {business.full_address && (
            <div className="flex items-center text-sm text-slate-600">
              <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
              <span className="truncate">{business.full_address}</span>
            </div>
          )}
          
          {business.phone && (
            <div className="flex items-center text-sm text-slate-600">
              <Phone className="h-4 w-4 mr-1 flex-shrink-0" />
              <span>{business.phone}</span>
            </div>
          )}
          
          {business.website && (
            <div className="flex items-center text-sm text-slate-600">
              <Globe className="h-4 w-4 mr-1 flex-shrink-0" />
              <a 
                href={business.website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-[#007749] hover:underline truncate"
              >
                Visit Website
              </a>
            </div>
          )}
        </div>
        
        {business.description && (
          <p className="text-sm text-slate-600 mb-3 line-clamp-2">
            {business.description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex items-center text-amber-500">
              <Star className="h-4 w-4 fill-current" />
              <span className="ml-1 text-sm font-medium">4.5</span>
            </div>
            <span className="mx-2 text-slate-300">•</span>
            <span className="text-sm text-slate-500">Reviews</span>
          </div>
          
          {business.price_range && (
            <Badge variant="outline" className="text-xs">
              {business.price_range}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default BusinessCard;
