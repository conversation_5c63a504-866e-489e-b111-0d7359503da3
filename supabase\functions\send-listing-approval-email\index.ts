import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[LISTING-APPROVAL-EMAIL] ${step}${detailsStr}`);
};

interface ListingData {
  id: string;
  title: string;
  category_id: string;
  description: string;
  full_address: string;
  city: string;
  phone: string;
  website: string;
  user_id: string;
  created_at: string;
}

interface CategoryData {
  name: string;
  slug: string;
}

interface UserData {
  email: string;
  full_name: string;
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const { listingId } = await req.json();
    logStep("Request data", { listingId });

    if (!listingId) {
      throw new Error("Listing ID is required");
    }

    // Fetch listing details with category and user information
    const { data: listing, error: listingError } = await supabaseClient
      .from('business_listings')
      .select(`
        *,
        categories:category_id (name, slug),
        profiles:user_id (email, full_name)
      `)
      .eq('id', listingId)
      .single();

    if (listingError || !listing) {
      logStep("Error fetching listing", listingError);
      throw new Error("Failed to fetch listing details");
    }

    logStep("Listing fetched", { title: listing.title, status: listing.status });

    // Only send email for pending listings
    if (listing.status !== 'pending') {
      logStep("Listing not pending, skipping email", { status: listing.status });
      return new Response(JSON.stringify({ message: "Email not sent - listing not pending" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }

    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    if (!resendApiKey) {
      throw new Error("Resend API key not configured");
    }

    // Generate approval token (simple approach using listing ID and timestamp)
    const approvalToken = btoa(`${listingId}:${Date.now()}`);
    const approvalUrl = `${Deno.env.get("SUPABASE_URL")}/functions/v1/approve-listing?token=${approvalToken}`;
    const dashboardUrl = `${req.headers.get("origin") || "https://sa360.co.za"}/admin/listings`;

    // Prepare email content
    const categoryName = listing.categories?.name || 'Uncategorized';
    const userEmail = listing.profiles?.email || 'Unknown';
    const userName = listing.profiles?.full_name || 'Unknown User';

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>New Listing Approval Required</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .listing-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .button { display: inline-block; background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
            .button.secondary { background: #dc2626; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Listing Approval Required</h1>
            </div>
            
            <div class="content">
              <p>A new business listing has been submitted and requires approval:</p>
              
              <div class="listing-details">
                <h3>${listing.title}</h3>
                <p><strong>Category:</strong> ${categoryName}</p>
                <p><strong>Location:</strong> ${listing.city}${listing.full_address ? `, ${listing.full_address}` : ''}</p>
                <p><strong>Phone:</strong> ${listing.phone || 'Not provided'}</p>
                <p><strong>Website:</strong> ${listing.website || 'Not provided'}</p>
                <p><strong>Submitted by:</strong> ${userName} (${userEmail})</p>
                <p><strong>Submitted on:</strong> ${new Date(listing.created_at).toLocaleString()}</p>
                
                ${listing.description ? `
                  <p><strong>Description:</strong></p>
                  <p>${listing.description}</p>
                ` : ''}
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${approvalUrl}&action=approve" class="button">✅ Approve Listing</a>
                <a href="${approvalUrl}&action=reject" class="button secondary">❌ Reject Listing</a>
              </div>
              
              <p>You can also manage all listings in the <a href="${dashboardUrl}">admin dashboard</a>.</p>
              
              <p><small>This email was automatically generated when a new listing was submitted to SA Business Beacon.</small></p>
            </div>
            
            <div class="footer">
              <p>SA Business Beacon - Business Directory</p>
              <p>This is an automated notification email.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    // Send email via Resend
    const emailResponse = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${resendApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: "SA Business Beacon <<EMAIL>>",
        to: ["<EMAIL>"],
        subject: `New Listing Approval Required: ${listing.title}`,
        html: emailHtml,
      }),
    });

    if (!emailResponse.ok) {
      const errorText = await emailResponse.text();
      logStep("Resend API error", { status: emailResponse.status, error: errorText });
      throw new Error(`Failed to send email: ${errorText}`);
    }

    const emailResult = await emailResponse.json();
    logStep("Email sent successfully", { emailId: emailResult.id });

    // Store the approval token for later verification
    const { error: tokenError } = await supabaseClient
      .from('listing_approval_tokens')
      .insert({
        listing_id: listingId,
        token: approvalToken,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      });

    if (tokenError) {
      logStep("Warning: Could not store approval token", tokenError);
      // Don't fail the email sending for this
    }

    return new Response(JSON.stringify({ 
      success: true, 
      message: "Approval email sent successfully",
      emailId: emailResult.id 
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
