import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage, Language, languageNames, languageFlags } from '@/contexts/LanguageContext';
import { Globe, Check } from 'lucide-react';

interface LanguageSelectorProps {
  variant?: 'default' | 'compact';
  className?: string;
}

const LanguageSelector = ({ variant = 'default', className }: LanguageSelectorProps) => {
  const { currentLanguage, setLanguage, t } = useLanguage();

  const languages: { code: Language; name: string; flag: string }[] = [
    { code: 'en', name: languageNames.en, flag: languageFlags.en },
    { code: 'af', name: languageNames.af, flag: languageFlags.af },
    { code: 'zu', name: languageNames.zu, flag: languageFlags.zu },
  ];

  const currentLang = languages.find(lang => lang.code === currentLanguage);

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className={`px-2 ${className}`}>
            <span className="text-lg mr-1">{currentLang?.flag}</span>
            <span className="text-sm font-medium">{currentLang?.code.toUpperCase()}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {languages.map((language) => (
            <DropdownMenuItem
              key={language.code}
              onClick={() => setLanguage(language.code)}
              className="flex items-center justify-between cursor-pointer"
            >
              <div className="flex items-center gap-2">
                <span className="text-lg">{language.flag}</span>
                <span>{language.name}</span>
              </div>
              {currentLanguage === language.code && (
                <Check className="h-4 w-4 text-[#007749]" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className={`flex items-center gap-2 ${className}`}>
          <Globe className="h-4 w-4" />
          <span className="text-lg">{currentLang?.flag}</span>
          <span className="hidden sm:inline">{currentLang?.name}</span>
          <span className="sm:hidden">{currentLang?.code.toUpperCase()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="px-2 py-1.5 text-sm font-semibold text-slate-700 border-b">
          {t('language.select', 'Select Language')}
        </div>
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => setLanguage(language.code)}
            className="flex items-center justify-between cursor-pointer py-2"
          >
            <div className="flex items-center gap-3">
              <span className="text-xl">{language.flag}</span>
              <div>
                <div className="font-medium">{language.name}</div>
                <div className="text-xs text-slate-500 capitalize">
                  {language.code === 'en' && 'English'}
                  {language.code === 'af' && 'Afrikaans'}
                  {language.code === 'zu' && 'isiZulu'}
                </div>
              </div>
            </div>
            {currentLanguage === language.code && (
              <Check className="h-4 w-4 text-[#007749]" />
            )}
          </DropdownMenuItem>
        ))}
        
        <div className="px-2 py-1.5 text-xs text-slate-500 border-t mt-1">
          {t('language.auto_translate', 'Auto-translation available')}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSelector;
