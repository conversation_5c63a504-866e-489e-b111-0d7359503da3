import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';

export type Language = 'en' | 'af' | 'zu';

interface LanguageContextType {
  currentLanguage: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, fallback?: string) => string;
  isLoading: boolean;
  translations: Record<string, string>;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

const languageNames = {
  en: 'English',
  af: 'Afrikaans',
  zu: 'isiZulu'
};

const languageFlags = {
  en: '🇬🇧',
  af: '🇿🇦',
  zu: '🇿🇦'
};

export const LanguageProvider = ({ children }: LanguageProviderProps) => {
  const { user } = useAuth();
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Load user's language preference
  useEffect(() => {
    const loadUserLanguagePreference = async () => {
      if (!user) {
        // Load from localStorage for non-authenticated users
        const savedLanguage = localStorage.getItem('preferred_language') as Language;
        if (savedLanguage && ['en', 'af', 'zu'].includes(savedLanguage)) {
          setCurrentLanguage(savedLanguage);
        }
        return;
      }

      try {
        const { data, error } = await supabase
          .from('user_language_preferences')
          .select('preferred_language')
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error loading language preference:', error);
          return;
        }

        if (data?.preferred_language) {
          setCurrentLanguage(data.preferred_language as Language);
        }
      } catch (error) {
        console.error('Error loading language preference:', error);
      }
    };

    loadUserLanguagePreference();
  }, [user]);

  // Load translations for current language
  useEffect(() => {
    const loadTranslations = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('ui_translations')
          .select('translation_key, translation_value')
          .eq('language_code', currentLanguage);

        if (error) throw error;

        const translationMap: Record<string, string> = {};
        data?.forEach(item => {
          translationMap[item.translation_key] = item.translation_value;
        });

        setTranslations(translationMap);
      } catch (error) {
        console.error('Error loading translations:', error);
        // Fallback to empty translations
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslations();
  }, [currentLanguage]);

  const setLanguage = async (language: Language) => {
    setCurrentLanguage(language);
    
    // Save to localStorage for immediate persistence
    localStorage.setItem('preferred_language', language);

    // Save to database if user is authenticated
    if (user) {
      try {
        const { error } = await supabase
          .from('user_language_preferences')
          .upsert({
            user_id: user.id,
            preferred_language: language,
            updated_at: new Date().toISOString()
          });

        if (error) {
          console.error('Error saving language preference:', error);
        }
      } catch (error) {
        console.error('Error saving language preference:', error);
      }
    }
  };

  const t = (key: string, fallback?: string): string => {
    // Return translation if available
    if (translations[key]) {
      return translations[key];
    }

    // Return fallback if provided
    if (fallback) {
      return fallback;
    }

    // Return the key itself as last resort (useful for development)
    return key;
  };

  const value: LanguageContextType = {
    currentLanguage,
    setLanguage,
    t,
    isLoading,
    translations
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Hook for getting business translations
export const useBusinessTranslation = (businessId: string) => {
  const { currentLanguage } = useLanguage();
  const [translatedBusiness, setTranslatedBusiness] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadBusinessTranslation = async () => {
      if (!businessId) return;

      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .rpc('get_business_with_translation', {
            business_id_param: businessId,
            language_code_param: currentLanguage
          });

        if (error) throw error;

        setTranslatedBusiness(data?.[0] || null);
      } catch (error) {
        console.error('Error loading business translation:', error);
        setTranslatedBusiness(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadBusinessTranslation();
  }, [businessId, currentLanguage]);

  return { translatedBusiness, isLoading };
};

// Hook for getting category translations
export const useCategoryTranslations = () => {
  const { currentLanguage } = useLanguage();
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadCategoryTranslations = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('categories')
          .select(`
            *,
            category_translations!inner (
              name,
              description,
              language_code
            )
          `)
          .eq('category_translations.language_code', currentLanguage);

        if (error) throw error;

        // Transform data to include translated names
        const translatedCategories = data?.map(category => ({
          ...category,
          name: category.category_translations?.[0]?.name || category.name,
          description: category.category_translations?.[0]?.description || category.description
        })) || [];

        setCategories(translatedCategories);
      } catch (error) {
        console.error('Error loading category translations:', error);
        // Fallback to original categories
        const { data } = await supabase.from('categories').select('*');
        setCategories(data || []);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategoryTranslations();
  }, [currentLanguage]);

  return { categories, isLoading };
};

export { languageNames, languageFlags };
export type { LanguageContextType };
