
import React from "react";
import Layout from "@/components/layout/Layout";
import SEOHead from "@/components/seo/SEOHead";
import { Link } from "react-router-dom";

type Category = {
  id: string;
  name: string;
  icon: string;
  count: number;
  description: string;
};

const allCategories: Category[] = [
  { id: "restaurants", name: "Restaurants", icon: "🍽️", count: 1250, description: "Find the best dining experiences across South Africa" },
  { id: "accommodation", name: "Accommodation", icon: "🏨", count: 876, description: "Hotels, guesthouses, and B&Bs for every budget" },
  { id: "retail", name: "Retail Stores", icon: "🛒", count: 1843, description: "Shopping destinations and retail outlets" },
  { id: "health", name: "Healthcare", icon: "🏥", count: 643, description: "Medical centers, clinics and healthcare providers" },
  { id: "automotive", name: "Automotive", icon: "🚗", count: 925, description: "Car dealerships, mechanics and auto services" },
  { id: "education", name: "Education", icon: "🎓", count: 487, description: "Schools, universities, and learning centers" },
  { id: "finance", name: "Financial Services", icon: "💰", count: 372, description: "Banks, insurance, and financial advisors" },
  { id: "technology", name: "Technology", icon: "💻", count: 564, description: "IT companies, tech retailers and service providers" },
  { id: "professional", name: "Professional Services", icon: "👔", count: 735, description: "Legal, accounting, consulting and more" },
  { id: "beauty", name: "Beauty & Wellness", icon: "💇‍♀️", count: 892, description: "Salons, spas, and wellness centers" },
  { id: "construction", name: "Construction", icon: "🏗️", count: 456, description: "Builders, contractors, and construction suppliers" },
  { id: "entertainment", name: "Entertainment", icon: "🎭", count: 623, description: "Theaters, venues, and entertainment centers" },
];

const Categories = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Business Categories in South Africa",
    "description": "Browse business categories available on SA360",
    "numberOfItems": allCategories.length,
    "itemListElement": allCategories.map((category, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Thing",
        "name": category.name,
        "description": category.description,
        "url": `https://sa360.co.za/category/${category.id}`
      }
    }))
  };

  return (
    <>
      <SEOHead
        title="Business Categories in South Africa - Browse by Industry"
        description="Explore all business categories on SA360. Find restaurants, accommodation, retail, healthcare, automotive, education, finance, technology and professional services across South Africa."
        keywords="business categories South Africa, SA business types, restaurant directory, accommodation listings, retail stores SA, healthcare providers, automotive services"
        structuredData={structuredData}
      />
      <Layout>
        <div className="py-12 bg-slate-50">
          <div className="container mx-auto px-4">
            <header className="text-center mb-12">
              <h1 className="text-3xl md:text-4xl font-bold mb-4 text-slate-800">
                Business Categories in South Africa
              </h1>
              <p className="text-slate-600 max-w-3xl mx-auto mb-8">
                Browse through our comprehensive list of business categories across South Africa. 
                From restaurants and accommodation to professional services and healthcare, 
                find exactly what you're looking for in your area.
              </p>
            </header>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {allCategories.map((category) => (
                <Link
                  to={`/category/${category.id}`}
                  key={category.id}
                  className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 flex flex-col items-center group"
                >
                  <div className="text-5xl mb-4" role="img" aria-label={category.name}>
                    {category.icon}
                  </div>
                  <h2 className="text-xl font-semibold text-slate-800 group-hover:text-[#007749] mb-2">
                    {category.name}
                  </h2>
                  <p className="text-slate-500 text-center mb-3">{category.description}</p>
                  <p className="text-sm font-medium text-[#003087]">{category.count} businesses</p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default Categories;
