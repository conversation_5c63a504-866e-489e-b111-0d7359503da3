import React, { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, MapPin, Filter } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const BusinessesSimple = () => {
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBusinesses = async () => {
      try {
        console.log('Fetching businesses...');
        
        // Simple query without joins first
        const { data, error } = await supabase
          .from('business_listings')
          .select('*')
          .eq('status', 'approved')
          .limit(10);

        if (error) {
          console.error('Supabase error:', error);
          setError(error.message);
          return;
        }

        console.log('Fetched data:', data);
        setBusinesses(data || []);
      } catch (err) {
        console.error('Unexpected error:', err);
        setError('Unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBusinesses();
  }, []);

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800">Businesses in South Africa (Simple)</h1>
          
          {/* Search section */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search businesses..."
                  className="pl-10"
                />
              </div>
              <div className="flex-1 relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Location..."
                  className="pl-10"
                />
              </div>
              <Button className="bg-[#007749] hover:bg-[#006739] px-8">
                Search
              </Button>
            </div>
          </div>
          
          {/* Loading state */}
          {isLoading && (
            <div className="text-center py-12">
              <p>Loading businesses...</p>
            </div>
          )}

          {/* Error state */}
          {error && (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">Error: {error}</p>
              <Button onClick={() => window.location.reload()}>
                Reload Page
              </Button>
            </div>
          )}
          
          {/* Results */}
          {!isLoading && !error && (
            <div>
              <p className="mb-6 text-slate-600">Found {businesses.length} businesses</p>
              
              {businesses.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {businesses.map((business) => (
                    <div key={business.id} className="bg-white rounded-lg shadow-sm border p-4">
                      <h3 className="font-semibold text-lg mb-2">{business.title}</h3>
                      <p className="text-slate-600 text-sm mb-2">{business.description}</p>
                      <p className="text-slate-500 text-sm">{business.city}</p>
                      <p className="text-slate-500 text-sm">Status: {business.status}</p>
                      <p className="text-slate-500 text-sm">Tier: {business.subscription_tier || 'free'}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-slate-600">No businesses found.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default BusinessesSimple;
