-- Create business reviews table (enhanced version)
CREATE TABLE public.business_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_title TEXT,
  review_text TEXT,
  verified_purchase BOOLEAN DEFAULT FALSE,
  helpful_votes INTEGER DEFAULT 0,
  total_votes INTEGER DEFAULT 0,
  response_from_business TEXT, -- Business owner response
  response_date TIMESTAMP WITH TIME ZONE,
  images TEXT[], -- Review images
  visit_date DATE, -- When they visited the business
  recommended BOOLEAN, -- Would they recommend this business
  status TEXT DEFAULT 'published' CHECK (status IN ('published', 'pending', 'hidden', 'flagged')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create review rewards/points system
CREATE TABLE public.review_rewards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  review_id UUID REFERENCES public.business_reviews(id) ON DELETE CASCADE,
  points_earned INTEGER NOT NULL DEFAULT 0,
  reward_type TEXT NOT NULL CHECK (reward_type IN ('review_points', 'photo_bonus', 'verified_bonus', 'helpful_bonus')),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user points/rewards balance
CREATE TABLE public.user_rewards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  total_points INTEGER DEFAULT 0,
  available_points INTEGER DEFAULT 0, -- Points not yet redeemed
  total_reviews INTEGER DEFAULT 0,
  verified_reviews INTEGER DEFAULT 0,
  helpful_reviews INTEGER DEFAULT 0, -- Reviews marked as helpful by others
  reputation_score DECIMAL(5,2) DEFAULT 0.00, -- Overall reputation score
  badges JSONB DEFAULT '[]', -- Array of earned badges
  level_name TEXT DEFAULT 'Newcomer',
  level_number INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create business reputation scores
CREATE TABLE public.business_reputation (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE UNIQUE,
  average_rating DECIMAL(3,2) DEFAULT 0.00,
  total_reviews INTEGER DEFAULT 0,
  total_ratings INTEGER DEFAULT 0,
  rating_distribution JSONB DEFAULT '{"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}',
  response_rate DECIMAL(5,2) DEFAULT 0.00, -- Percentage of reviews responded to
  average_response_time INTEGER, -- Average response time in hours
  verified_reviews INTEGER DEFAULT 0,
  recommended_percentage DECIMAL(5,2) DEFAULT 0.00,
  reputation_badges JSONB DEFAULT '[]', -- Badges like "Quick Response", "Highly Rated", etc.
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create review helpfulness votes
CREATE TABLE public.review_votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID REFERENCES public.business_reviews(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  vote_type TEXT NOT NULL CHECK (vote_type IN ('helpful', 'not_helpful')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(review_id, user_id)
);

-- Create reward redemptions table
CREATE TABLE public.reward_redemptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  points_used INTEGER NOT NULL,
  reward_type TEXT NOT NULL, -- 'discount_code', 'gift_voucher', 'premium_features', etc.
  reward_value TEXT NOT NULL, -- The actual discount code or reward details
  business_id UUID REFERENCES public.business_listings(id) ON DELETE SET NULL, -- If reward is business-specific
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'used', 'expired')),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to update business reputation when review is added/updated
CREATE OR REPLACE FUNCTION update_business_reputation()
RETURNS TRIGGER AS $$
BEGIN
  -- Update business reputation scores
  INSERT INTO public.business_reputation (business_id, average_rating, total_reviews, total_ratings, rating_distribution, verified_reviews, recommended_percentage)
  SELECT 
    NEW.business_id,
    ROUND(AVG(rating::DECIMAL), 2) as average_rating,
    COUNT(*) as total_reviews,
    COUNT(*) as total_ratings,
    jsonb_build_object(
      '1', COUNT(*) FILTER (WHERE rating = 1),
      '2', COUNT(*) FILTER (WHERE rating = 2),
      '3', COUNT(*) FILTER (WHERE rating = 3),
      '4', COUNT(*) FILTER (WHERE rating = 4),
      '5', COUNT(*) FILTER (WHERE rating = 5)
    ) as rating_distribution,
    COUNT(*) FILTER (WHERE verified_purchase = true) as verified_reviews,
    ROUND(
      (COUNT(*) FILTER (WHERE recommended = true)::DECIMAL / 
       NULLIF(COUNT(*) FILTER (WHERE recommended IS NOT NULL), 0)) * 100, 2
    ) as recommended_percentage
  FROM public.business_reviews 
  WHERE business_id = NEW.business_id AND status = 'published'
  ON CONFLICT (business_id) 
  DO UPDATE SET
    average_rating = EXCLUDED.average_rating,
    total_reviews = EXCLUDED.total_reviews,
    total_ratings = EXCLUDED.total_ratings,
    rating_distribution = EXCLUDED.rating_distribution,
    verified_reviews = EXCLUDED.verified_reviews,
    recommended_percentage = EXCLUDED.recommended_percentage,
    last_updated = NOW();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to award points for reviews
CREATE OR REPLACE FUNCTION award_review_points()
RETURNS TRIGGER AS $$
DECLARE
  base_points INTEGER := 10;
  bonus_points INTEGER := 0;
  total_points INTEGER;
BEGIN
  -- Calculate bonus points
  IF NEW.review_text IS NOT NULL AND LENGTH(NEW.review_text) > 50 THEN
    bonus_points := bonus_points + 5; -- Detailed review bonus
  END IF;
  
  IF NEW.images IS NOT NULL AND array_length(NEW.images, 1) > 0 THEN
    bonus_points := bonus_points + 10; -- Photo bonus
  END IF;
  
  IF NEW.verified_purchase = true THEN
    bonus_points := bonus_points + 15; -- Verified purchase bonus
  END IF;

  total_points := base_points + bonus_points;

  -- Insert reward record
  INSERT INTO public.review_rewards (user_id, review_id, points_earned, reward_type, description)
  VALUES (NEW.user_id, NEW.id, total_points, 'review_points', 'Points earned for writing a review');

  -- Update user rewards
  INSERT INTO public.user_rewards (user_id, total_points, available_points, total_reviews)
  VALUES (NEW.user_id, total_points, total_points, 1)
  ON CONFLICT (user_id)
  DO UPDATE SET
    total_points = user_rewards.total_points + total_points,
    available_points = user_rewards.available_points + total_points,
    total_reviews = user_rewards.total_reviews + 1,
    updated_at = NOW();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update user reputation when review gets helpful votes
CREATE OR REPLACE FUNCTION update_user_reputation()
RETURNS TRIGGER AS $$
BEGIN
  -- Update helpful reviews count and reputation score
  UPDATE public.user_rewards 
  SET 
    helpful_reviews = (
      SELECT COUNT(DISTINCT br.id)
      FROM public.business_reviews br
      WHERE br.user_id = (SELECT user_id FROM public.business_reviews WHERE id = NEW.review_id)
      AND br.helpful_votes > 0
    ),
    reputation_score = LEAST(100.00, 
      (total_reviews * 2) + 
      (helpful_reviews * 5) + 
      (verified_reviews * 3)
    ),
    updated_at = NOW()
  WHERE user_id = (SELECT user_id FROM public.business_reviews WHERE id = NEW.review_id);

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_update_business_reputation
  AFTER INSERT OR UPDATE ON public.business_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_business_reputation();

CREATE TRIGGER trigger_award_review_points
  AFTER INSERT ON public.business_reviews
  FOR EACH ROW
  EXECUTE FUNCTION award_review_points();

CREATE TRIGGER trigger_update_user_reputation
  AFTER INSERT ON public.review_votes
  FOR EACH ROW
  EXECUTE FUNCTION update_user_reputation();

-- Insert some sample reward levels and badges
INSERT INTO public.user_rewards (user_id, level_name, level_number) VALUES
('00000000-0000-0000-0000-000000000001', 'Newcomer', 1),
('00000000-0000-0000-0000-000000000002', 'Reviewer', 2),
('00000000-0000-0000-0000-000000000003', 'Expert Reviewer', 3),
('00000000-0000-0000-0000-000000000004', 'Review Master', 4),
('00000000-0000-0000-0000-000000000005', 'Review Legend', 5)
ON CONFLICT (user_id) DO NOTHING;

-- Add RLS policies
ALTER TABLE public.business_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_reputation ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reward_redemptions ENABLE ROW LEVEL SECURITY;

-- Business reviews policies
CREATE POLICY "Anyone can view published reviews" ON public.business_reviews
  FOR SELECT USING (status = 'published');

CREATE POLICY "Users can create reviews" ON public.business_reviews
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" ON public.business_reviews
  FOR UPDATE USING (auth.uid() = user_id);

-- Business owners can respond to reviews
CREATE POLICY "Business owners can respond to reviews" ON public.business_reviews
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Review rewards policies
CREATE POLICY "Users can view their own rewards" ON public.review_rewards
  FOR SELECT USING (auth.uid() = user_id);

-- User rewards policies
CREATE POLICY "Users can view their own reward status" ON public.user_rewards
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view public reputation scores" ON public.business_reputation
  FOR SELECT USING (true);

-- Review votes policies
CREATE POLICY "Users can vote on reviews" ON public.review_votes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view all review votes" ON public.review_votes
  FOR SELECT USING (true);

-- Reward redemptions policies
CREATE POLICY "Users can manage their own redemptions" ON public.reward_redemptions
  FOR ALL USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_business_reviews_business_id ON public.business_reviews(business_id);
CREATE INDEX idx_business_reviews_user_id ON public.business_reviews(user_id);
CREATE INDEX idx_business_reviews_rating ON public.business_reviews(rating);
CREATE INDEX idx_business_reviews_created_at ON public.business_reviews(created_at);
CREATE INDEX idx_review_rewards_user_id ON public.review_rewards(user_id);
CREATE INDEX idx_user_rewards_user_id ON public.user_rewards(user_id);
CREATE INDEX idx_business_reputation_business_id ON public.business_reputation(business_id);
CREATE INDEX idx_review_votes_review_id ON public.review_votes(review_id);
CREATE INDEX idx_reward_redemptions_user_id ON public.reward_redemptions(user_id);
