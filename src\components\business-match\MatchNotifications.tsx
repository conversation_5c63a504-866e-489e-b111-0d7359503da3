import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bell, BellOff, Eye, EyeOff, Star, MapPin } from 'lucide-react';
import { toast } from 'sonner';

interface MatchNotification {
  id: string;
  business_id: string;
  match_score: number;
  notification_type: string;
  message: string | null;
  is_read: boolean;
  sent_at: string;
  business_listings: {
    title: string;
    description: string | null;
    city: string | null;
    logo_url: string | null;
    categories?: {
      name: string;
    };
  };
}

const MatchNotifications = () => {
  const { user } = useAuth();
  const [customerProfileId, setCustomerProfileId] = useState<string | null>(null);

  // Get customer profile ID
  useEffect(() => {
    const getCustomerProfile = async () => {
      if (!user) return;
      
      const { data } = await supabase
        .from('customer_profiles')
        .select('id')
        .eq('user_id', user.id)
        .single();
      
      if (data) {
        setCustomerProfileId(data.id);
      }
    };

    getCustomerProfile();
  }, [user]);

  const { data: notifications, isLoading, refetch } = useQuery({
    queryKey: ['match-notifications', customerProfileId],
    queryFn: async () => {
      if (!customerProfileId) return [];

      const { data, error } = await supabase
        .from('match_notifications')
        .select(`
          *,
          business_listings (
            title,
            description,
            city,
            logo_url,
            categories (
              name
            )
          )
        `)
        .eq('customer_profile_id', customerProfileId)
        .order('sent_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      return data as MatchNotification[];
    },
    enabled: !!customerProfileId,
  });

  const markAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('match_notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;
      
      refetch();
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to update notification');
    }
  };

  const markAllAsRead = async () => {
    if (!customerProfileId) return;

    try {
      const { error } = await supabase
        .from('match_notifications')
        .update({ is_read: true })
        .eq('customer_profile_id', customerProfileId)
        .eq('is_read', false);

      if (error) throw error;
      
      refetch();
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all as read:', error);
      toast.error('Failed to update notifications');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_match':
        return <Star className="h-4 w-4 text-[#007749]" />;
      case 'updated_match':
        return <Bell className="h-4 w-4 text-blue-600" />;
      case 'special_offer':
        return <Badge className="h-4 w-4 text-orange-600" />;
      default:
        return <Bell className="h-4 w-4 text-slate-600" />;
    }
  };

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'new_match':
        return 'New Match';
      case 'updated_match':
        return 'Updated Match';
      case 'special_offer':
        return 'Special Offer';
      default:
        return 'Notification';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <BellOff className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to view your match notifications</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading notifications...</p>
        </CardContent>
      </Card>
    );
  }

  const unreadCount = notifications?.filter(n => !n.is_read).length || 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-[#007749]" />
              Match Notifications
              {unreadCount > 0 && (
                <Badge className="bg-red-100 text-red-800">
                  {unreadCount} new
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Get notified about new business matches and updates
            </CardDescription>
          </div>
          {unreadCount > 0 && (
            <Button variant="outline" size="sm" onClick={markAllAsRead}>
              <EyeOff className="h-4 w-4 mr-2" />
              Mark all read
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {!notifications || notifications.length === 0 ? (
          <div className="text-center py-8">
            <BellOff className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 mb-2">No notifications yet</p>
            <p className="text-sm text-slate-500">
              Complete a business match to start receiving personalized recommendations
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {notifications.map((notification) => (
              <Card 
                key={notification.id} 
                className={`cursor-pointer transition-colors ${
                  !notification.is_read ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={() => !notification.is_read && markAsRead(notification.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    {notification.business_listings.logo_url ? (
                      <img 
                        src={notification.business_listings.logo_url} 
                        alt={notification.business_listings.title}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center">
                        {getNotificationIcon(notification.notification_type)}
                      </div>
                    )}

                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {getNotificationTypeLabel(notification.notification_type)}
                        </Badge>
                        <Badge className="bg-[#007749] text-white text-xs">
                          {notification.match_score}% match
                        </Badge>
                        {!notification.is_read && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>

                      <h3 className="font-semibold text-slate-800 mb-1">
                        {notification.business_listings.title}
                      </h3>

                      {notification.message && (
                        <p className="text-sm text-slate-600 mb-2">
                          {notification.message}
                        </p>
                      )}

                      <div className="flex items-center gap-4 text-xs text-slate-500">
                        {notification.business_listings.categories && (
                          <span>{notification.business_listings.categories.name}</span>
                        )}
                        {notification.business_listings.city && (
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {notification.business_listings.city}
                          </span>
                        )}
                        <span>{new Date(notification.sent_at).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {!notification.is_read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          markAsRead(notification.id);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MatchNotifications;
