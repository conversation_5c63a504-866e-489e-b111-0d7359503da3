import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[LISTING-APPROVAL] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const url = new URL(req.url);
    const token = url.searchParams.get('token');
    const action = url.searchParams.get('action'); // 'approve' or 'reject'

    logStep("Request params", { token: token?.substring(0, 20) + '...', action });

    if (!token || !action) {
      throw new Error("Token and action parameters are required");
    }

    if (!['approve', 'reject'].includes(action)) {
      throw new Error("Invalid action. Must be 'approve' or 'reject'");
    }

    // Decode the token to get listing ID and timestamp
    let listingId: string;
    let tokenTimestamp: number;
    
    try {
      const decoded = atob(token);
      const [id, timestamp] = decoded.split(':');
      listingId = id;
      tokenTimestamp = parseInt(timestamp);
    } catch (error) {
      throw new Error("Invalid token format");
    }

    logStep("Token decoded", { listingId, tokenTimestamp });

    // Check if token is expired (7 days)
    const tokenAge = Date.now() - tokenTimestamp;
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    
    if (tokenAge > maxAge) {
      throw new Error("Approval token has expired");
    }

    // Fetch the listing to verify it exists and get current status
    const { data: listing, error: listingError } = await supabaseClient
      .from('business_listings')
      .select('id, title, status, user_id')
      .eq('id', listingId)
      .single();

    if (listingError || !listing) {
      logStep("Error fetching listing", listingError);
      throw new Error("Listing not found");
    }

    logStep("Listing found", { title: listing.title, currentStatus: listing.status });

    // Check if listing is still pending
    if (listing.status !== 'pending') {
      const message = `Listing has already been ${listing.status}`;
      logStep("Listing already processed", { status: listing.status });
      
      return new Response(generateHtmlResponse(
        "Already Processed",
        message,
        `The listing "${listing.title}" has already been ${listing.status}.`,
        "warning"
      ), {
        headers: { ...corsHeaders, "Content-Type": "text/html" },
        status: 200,
      });
    }

    // Update the listing status
    const newStatus = action === 'approve' ? 'approved' : 'rejected';
    const { error: updateError } = await supabaseClient
      .from('business_listings')
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', listingId);

    if (updateError) {
      logStep("Error updating listing", updateError);
      throw new Error("Failed to update listing status");
    }

    logStep("Listing status updated", { newStatus });

    // If approved, also update the listing to be featured for new businesses (optional)
    if (action === 'approve') {
      // You could add additional logic here, like:
      // - Setting featured status for first-time businesses
      // - Sending welcome email to the business owner
      // - Triggering other workflows
    }

    // Invalidate the token by deleting it (if we're storing tokens)
    await supabaseClient
      .from('listing_approval_tokens')
      .delete()
      .eq('token', token);

    // Generate success response
    const actionPast = action === 'approve' ? 'approved' : 'rejected';
    const message = `Listing ${actionPast} successfully`;
    const description = `The listing "${listing.title}" has been ${actionPast}.`;

    logStep("Success", { action: actionPast, listingTitle: listing.title });

    return new Response(generateHtmlResponse(
      "Success",
      message,
      description,
      action === 'approve' ? "success" : "error"
    ), {
      headers: { ...corsHeaders, "Content-Type": "text/html" },
      status: 200,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    
    return new Response(generateHtmlResponse(
      "Error",
      "Failed to process approval",
      errorMessage,
      "error"
    ), {
      headers: { ...corsHeaders, "Content-Type": "text/html" },
      status: 400,
    });
  }
});

function generateHtmlResponse(title: string, heading: string, message: string, type: "success" | "error" | "warning"): string {
  const colors = {
    success: { bg: "#dcfce7", border: "#16a34a", text: "#15803d" },
    error: { bg: "#fef2f2", border: "#dc2626", text: "#dc2626" },
    warning: { bg: "#fef3c7", border: "#d97706", text: "#d97706" }
  };

  const color = colors[type];

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>${title} - SA Business Beacon</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 20px;
            background: #f3f4f6;
          }
          .container { 
            max-width: 600px; 
            margin: 50px auto; 
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }
          .header { 
            background: #2563eb; 
            color: white; 
            padding: 30px; 
            text-align: center; 
          }
          .content { 
            padding: 40px; 
            text-align: center; 
          }
          .status-box {
            background: ${color.bg};
            border: 2px solid ${color.border};
            color: ${color.text};
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
          }
          .footer { 
            text-align: center; 
            padding: 20px; 
            background: #f9fafb;
            font-size: 14px; 
            color: #666; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>SA Business Beacon</h1>
            <p>Business Directory Management</p>
          </div>
          
          <div class="content">
            <div class="status-box">
              <h2>${heading}</h2>
              <p>${message}</p>
            </div>
            
            <a href="https://sa360.co.za/admin/listings" class="button">
              Go to Admin Dashboard
            </a>
          </div>
          
          <div class="footer">
            <p>SA Business Beacon - Automated Listing Management</p>
            <p>This action was processed automatically.</p>
          </div>
        </div>
      </body>
    </html>
  `;
}
