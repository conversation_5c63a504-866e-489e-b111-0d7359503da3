import React from 'react';
import Layout from '@/components/layout/Layout';
import ServiceProviderDirectory from '@/components/services/ServiceProviderDirectory';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Code, 
  Camera, 
  TrendingUp, 
  Palette, 
  PenTool, 
  MessageSquare,
  Calculator,
  Scale,
  Users
} from 'lucide-react';

const Services = () => {
  const serviceCategories = [
    {
      icon: <Code className="h-8 w-8" />,
      title: 'Web Development',
      description: 'Custom websites, e-commerce, web applications',
      providers: 45,
      avgPrice: 'R15,000 - R50,000'
    },
    {
      icon: <Camera className="h-8 w-8" />,
      title: 'Photography',
      description: 'Product photography, events, corporate headshots',
      providers: 32,
      avgPrice: 'R2,000 - R8,000'
    },
    {
      icon: <TrendingUp className="h-8 w-8" />,
      title: 'SEO & Marketing',
      description: 'Search optimization, digital marketing, PPC',
      providers: 28,
      avgPrice: 'R5,000 - R20,000'
    },
    {
      icon: <Palette className="h-8 w-8" />,
      title: 'Graphic Design',
      description: 'Logos, branding, marketing materials',
      providers: 38,
      avgPrice: 'R3,000 - R12,000'
    },
    {
      icon: <PenTool className="h-8 w-8" />,
      title: 'Content Writing',
      description: 'Website copy, blog posts, marketing content',
      providers: 24,
      avgPrice: 'R500 - R3,000'
    },
    {
      icon: <MessageSquare className="h-8 w-8" />,
      title: 'Social Media',
      description: 'Social media management, content creation',
      providers: 19,
      avgPrice: 'R3,000 - R10,000'
    },
    {
      icon: <Calculator className="h-8 w-8" />,
      title: 'Accounting',
      description: 'Bookkeeping, tax preparation, financial planning',
      providers: 15,
      avgPrice: 'R2,000 - R8,000'
    },
    {
      icon: <Scale className="h-8 w-8" />,
      title: 'Legal Services',
      description: 'Business law, contracts, compliance',
      providers: 12,
      avgPrice: 'R5,000 - R25,000'
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: 'Business Consulting',
      description: 'Strategy, operations, growth consulting',
      providers: 21,
      avgPrice: 'R8,000 - R30,000'
    }
  ];

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-slate-800">
              Business Services Marketplace
            </h1>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Connect with verified service providers to grow your business. From web development to marketing, 
              find the right professionals for your needs.
            </p>
          </div>

          <Tabs defaultValue="directory" className="space-y-8">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="directory">Find Providers</TabsTrigger>
              <TabsTrigger value="categories">Browse Categories</TabsTrigger>
            </TabsList>

            <TabsContent value="directory">
              <ServiceProviderDirectory />
            </TabsContent>

            <TabsContent value="categories">
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold mb-4">Service Categories</h2>
                  <p className="text-slate-600">
                    Explore our comprehensive range of business services
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {serviceCategories.map((category, index) => (
                    <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                      <CardHeader>
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-[#007749] bg-opacity-10 rounded-lg text-[#007749]">
                            {category.icon}
                          </div>
                          <CardTitle className="text-lg">{category.title}</CardTitle>
                        </div>
                        <CardDescription>{category.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">
                              {category.providers} providers
                            </Badge>
                          </div>
                          <span className="text-slate-600 font-medium">
                            {category.avgPrice}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Card className="bg-gradient-to-r from-[#007749] to-[#006739] text-white">
                  <CardContent className="p-8 text-center">
                    <h3 className="text-2xl font-bold mb-4">Become a Service Provider</h3>
                    <p className="text-lg mb-6 opacity-90">
                      Join our marketplace and connect with businesses looking for your expertise
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold">500+</div>
                        <div className="text-sm opacity-80">Active Businesses</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold">R2.5M+</div>
                        <div className="text-sm opacity-80">Projects Completed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold">4.8★</div>
                        <div className="text-sm opacity-80">Average Rating</div>
                      </div>
                    </div>
                    <button className="bg-white text-[#007749] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                      Apply to Join
                    </button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Services;
