import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface TrackingData {
  adId: string;
  eventType: 'impression' | 'click';
  viewDuration?: number;
  referrer?: string;
  pageUrl?: string;
}

export const useAdTracking = () => {
  const { user } = useAuth();

  const generateSessionId = useCallback(() => {
    return Math.random().toString(36).substr(2, 9);
  }, []);

  const getDeviceInfo = useCallback(() => {
    if (typeof window === 'undefined') return { deviceType: 'desktop', browser: 'unknown' };

    const userAgent = navigator.userAgent.toLowerCase();
    let deviceType = 'desktop';
    let browser = 'other';

    // Detect device type
    if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
      deviceType = 'mobile';
    } else if (userAgent.includes('tablet') || userAgent.includes('ipad')) {
      deviceType = 'tablet';
    }

    // Detect browser
    if (userAgent.includes('chrome')) {
      browser = 'Chrome';
    } else if (userAgent.includes('firefox')) {
      browser = 'Firefox';
    } else if (userAgent.includes('safari')) {
      browser = 'Safari';
    } else if (userAgent.includes('edge')) {
      browser = 'Edge';
    }

    return { deviceType, browser };
  }, []);

  const trackImpression = useCallback(async (data: TrackingData) => {
    try {
      const { deviceType, browser } = getDeviceInfo();
      
      await supabase.rpc('track_ad_impression', {
        ad_id_param: data.adId,
        user_id_param: user?.id || null,
        ip_address_param: null, // Would be set by server in production
        user_agent_param: typeof window !== 'undefined' ? navigator.userAgent : null,
        page_url_param: data.pageUrl || (typeof window !== 'undefined' ? window.location.href : null),
        session_id_param: generateSessionId(),
        view_duration_param: data.viewDuration || 0
      });
    } catch (error) {
      console.error('Error tracking impression:', error);
    }
  }, [user, getDeviceInfo, generateSessionId]);

  const trackClick = useCallback(async (data: TrackingData) => {
    try {
      const { deviceType, browser } = getDeviceInfo();
      
      await supabase.rpc('track_ad_click', {
        ad_id_param: data.adId,
        user_id_param: user?.id || null,
        ip_address_param: null, // Would be set by server in production
        user_agent_param: typeof window !== 'undefined' ? navigator.userAgent : null,
        referrer_param: data.referrer || (typeof window !== 'undefined' ? document.referrer : null),
        page_url_param: data.pageUrl || (typeof window !== 'undefined' ? window.location.href : null),
        session_id_param: generateSessionId()
      });

      // Update CTR after click
      await supabase.rpc('update_ad_ctr', { ad_id_param: data.adId });
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  }, [user, getDeviceInfo, generateSessionId]);

  const trackConversion = useCallback(async (adId: string, conversionValue?: number) => {
    try {
      // This would be implemented based on your conversion tracking needs
      // For now, we'll just log it
      console.log('Conversion tracked for ad:', adId, 'value:', conversionValue);
      
      // In a real implementation, you might:
      // 1. Update a conversions table
      // 2. Calculate conversion rates
      // 3. Update campaign performance metrics
    } catch (error) {
      console.error('Error tracking conversion:', error);
    }
  }, []);

  const getAdPerformance = useCallback(async (adId: string, timeRange: string = '7d') => {
    try {
      const timeFilter = getTimeFilter(timeRange);
      
      const [impressionsResult, clicksResult] = await Promise.all([
        supabase
          .from('ad_impressions')
          .select('*')
          .eq('ad_id', adId)
          .gte('viewed_at', timeFilter),
        supabase
          .from('ad_clicks')
          .select('*')
          .eq('ad_id', adId)
          .gte('clicked_at', timeFilter)
      ]);

      const impressions = impressionsResult.data || [];
      const clicks = clicksResult.data || [];

      // Calculate performance metrics
      const totalImpressions = impressions.length;
      const totalClicks = clicks.length;
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

      // Device breakdown
      const deviceBreakdown = clicks.reduce((acc, click) => {
        acc[click.device_type] = (acc[click.device_type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Browser breakdown
      const browserBreakdown = clicks.reduce((acc, click) => {
        acc[click.browser] = (acc[click.browser] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Hourly breakdown for the last 24 hours
      const hourlyData = Array.from({ length: 24 }, (_, hour) => {
        const hourClicks = clicks.filter(click => {
          const clickHour = new Date(click.clicked_at).getHours();
          return clickHour === hour;
        }).length;
        
        return {
          hour,
          clicks: hourlyClicks
        };
      });

      return {
        totalImpressions,
        totalClicks,
        ctr: parseFloat(ctr.toFixed(2)),
        deviceBreakdown,
        browserBreakdown,
        hourlyData,
        impressions,
        clicks
      };
    } catch (error) {
      console.error('Error getting ad performance:', error);
      return null;
    }
  }, []);

  const getTimeFilter = (range: string) => {
    const now = new Date();
    switch (range) {
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    }
  };

  return {
    trackImpression,
    trackClick,
    trackConversion,
    getAdPerformance,
    getDeviceInfo
  };
};

export default useAdTracking;
