import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User, MapPin, Phone, Mail } from 'lucide-react';

interface CustomerProfileFormProps {
  onSubmit: (profileData: any) => void;
  onCancel: () => void;
  initialData?: any;
}

const CustomerProfileForm = ({ onSubmit, onCancel, initialData }: CustomerProfileFormProps) => {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    email: initialData?.email || '',
    phone: initialData?.phone || '',
    location: initialData?.location || '',
    preferences: {
      preferredCategories: initialData?.preferences?.preferredCategories || [],
      budgetRange: initialData?.preferences?.budgetRange || '',
      maxDistance: initialData?.preferences?.maxDistance || '',
      notificationPreferences: initialData?.preferences?.notificationPreferences || 'email'
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const locations = [
    'Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth',
    'Bloemfontein', 'East London', 'Polokwane', 'Nelspruit', 'Kimberley'
  ];

  const categories = [
    'Restaurant', 'Accommodation', 'Professional Services', 'Retail',
    'Healthcare', 'Automotive', 'Entertainment', 'Beauty & Wellness', 'Technology'
  ];

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5 text-[#007749]" />
          Create Your Profile
        </CardTitle>
        <CardDescription>
          Help us provide better business recommendations by creating your profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Your full name"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+27 XX XXX XXXX"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="location">Primary Location</Label>
              <Select 
                value={formData.location} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, location: value }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select your city" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Preferences</h3>
            
            <div>
              <Label htmlFor="budgetRange">Typical Budget Range</Label>
              <Select 
                value={formData.preferences.budgetRange} 
                onValueChange={(value) => setFormData(prev => ({ 
                  ...prev, 
                  preferences: { ...prev.preferences, budgetRange: value }
                }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select budget range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="budget">Budget-friendly (Under R500)</SelectItem>
                  <SelectItem value="affordable">Affordable (R500-R1500)</SelectItem>
                  <SelectItem value="mid-range">Mid-range (R1500-R5000)</SelectItem>
                  <SelectItem value="premium">Premium (R5000-R15000)</SelectItem>
                  <SelectItem value="luxury">Luxury (R15000+)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="maxDistance">Maximum Travel Distance</Label>
              <Select 
                value={formData.preferences.maxDistance} 
                onValueChange={(value) => setFormData(prev => ({ 
                  ...prev, 
                  preferences: { ...prev.preferences, maxDistance: value }
                }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select distance" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5km">Within 5km</SelectItem>
                  <SelectItem value="15km">Within 15km</SelectItem>
                  <SelectItem value="30km">Within 30km</SelectItem>
                  <SelectItem value="city">Within the city</SelectItem>
                  <SelectItem value="province">Anywhere in province</SelectItem>
                  <SelectItem value="any">Distance doesn't matter</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notifications">Notification Preferences</Label>
              <Select 
                value={formData.preferences.notificationPreferences} 
                onValueChange={(value) => setFormData(prev => ({ 
                  ...prev, 
                  preferences: { ...prev.preferences, notificationPreferences: value }
                }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">Email notifications</SelectItem>
                  <SelectItem value="sms">SMS notifications</SelectItem>
                  <SelectItem value="both">Email and SMS</SelectItem>
                  <SelectItem value="none">No notifications</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              className="flex-1"
            >
              Skip for Now
            </Button>
            <Button 
              type="submit" 
              className="flex-1 bg-[#007749] hover:bg-[#006739]"
            >
              Save Profile
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default CustomerProfileForm;
