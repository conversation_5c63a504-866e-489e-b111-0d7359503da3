
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { createHash } from "https://deno.land/std@0.190.0/hash/mod.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[PAYFAST-SUBSCRIPTION] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError) throw new Error(`Authentication error: ${userError.message}`);
    const user = userData.user;
    if (!user?.email) throw new Error("User not authenticated or email not available");

    const { tier, businessId } = await req.json();
    logStep("Request data", { tier, businessId, userId: user.id });

    // PayFast pricing configuration
    const pricingConfig = {
      'silver': { amount: 49900, name: 'Silver Premium' }, // R499.00
      'gold': { amount: 89900, name: 'Gold Premium' }, // R899.00
      'platinum': { amount: 149900, name: 'Platinum Premium' } // R1499.00
    };

    const selectedPlan = pricingConfig[tier as keyof typeof pricingConfig];
    if (!selectedPlan) throw new Error("Invalid subscription tier");

    // PayFast configuration
    const merchantId = Deno.env.get("PAYFAST_MERCHANT_ID");
    const merchantKey = Deno.env.get("PAYFAST_MERCHANT_KEY");
    const passphrase = Deno.env.get("PAYFAST_PASSPHRASE");
    
    if (!merchantId || !merchantKey) {
      throw new Error("PayFast credentials not configured");
    }

    const origin = req.headers.get("origin") || "http://localhost:3000";
    const orderId = `sub_${Date.now()}_${user.id.slice(0, 8)}`;

    // PayFast payment data
    const paymentData = {
      merchant_id: merchantId,
      merchant_key: merchantKey,
      return_url: `${origin}/subscription-success`,
      cancel_url: `${origin}/pricing`,
      notify_url: `${Deno.env.get("SUPABASE_URL")}/functions/v1/payfast-webhook`,
      name_first: user.user_metadata?.full_name?.split(' ')[0] || "User",
      name_last: user.user_metadata?.full_name?.split(' ').slice(1).join(' ') || "Name",
      email_address: user.email,
      m_payment_id: orderId,
      amount: (selectedPlan.amount / 100).toFixed(2),
      item_name: selectedPlan.name,
      item_description: `${selectedPlan.name} subscription for business listing`,
      subscription_type: "1",
      billing_date: new Date().toISOString().split('T')[0],
      recurring_amount: (selectedPlan.amount / 100).toFixed(2),
      frequency: "3", // Monthly
      cycles: "0" // Indefinite
    };

    // Generate signature
    const generateSignature = (data: any, passphrase?: string) => {
      const params = Object.keys(data)
        .filter(key => data[key] !== "" && data[key] !== null && data[key] !== undefined)
        .sort()
        .map(key => `${key}=${encodeURIComponent(data[key])}`)
        .join("&");
      
      const signatureString = passphrase ? `${params}&passphrase=${passphrase}` : params;
      return createHash("md5").update(signatureString).toString();
    };

    const signature = generateSignature(paymentData, passphrase);
    
    // Store pending subscription
    const { error: insertError } = await supabaseClient.from("subscriptions").insert({
      user_id: user.id,
      business_id: businessId,
      tier,
      status: "pending",
      amount: selectedPlan.amount,
      currency: "ZAR",
      stripe_subscription_id: orderId, // Using this field for PayFast order ID
    });

    if (insertError) {
      logStep("Error storing subscription", insertError);
      throw new Error("Failed to store subscription");
    }

    logStep("Subscription stored successfully", { orderId, tier });

    // PayFast form HTML
    const formHTML = `
      <html>
        <head><title>Redirecting to PayFast...</title></head>
        <body>
          <form id="payfast-form" action="https://sandbox.payfast.co.za/eng/process" method="post">
            ${Object.entries(paymentData).map(([key, value]) => 
              `<input type="hidden" name="${key}" value="${value}">`
            ).join('')}
            <input type="hidden" name="signature" value="${signature}">
            <p>Redirecting to PayFast...</p>
            <input type="submit" value="Continue to PayFast">
          </form>
          <script>
            document.getElementById('payfast-form').submit();
          </script>
        </body>
      </html>
    `;

    return new Response(formHTML, {
      headers: { 
        ...corsHeaders, 
        "Content-Type": "text/html"
      },
      status: 200,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
