
import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, MapPin, Filter } from "lucide-react";
import { useBusinessListings } from "@/hooks/useBusinessListings";
import BusinessCard from "@/components/business/BusinessCard";
import AdBanner from "@/components/ads/AdBanner";
import AffiliateLinks from "@/components/affiliate/AffiliateLinks";

const Businesses = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [location, setLocation] = useState("");
  const [appliedFilters, setAppliedFilters] = useState<{
    searchTerm?: string;
    location?: string;
  }>({});

  const { data: businesses, isLoading, error } = useBusinessListings(appliedFilters);

  const handleSearch = () => {
    setAppliedFilters({
      searchTerm: searchTerm || undefined,
      location: location || undefined,
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="flex gap-8">
            <div className="flex-1">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800">Businesses in South Africa</h1>

          {/* Top Ad Banner */}
          <AdBanner position="top" className="mb-6" />
          
          {/* Search and filter section */}
          <div className="mb-8 bg-white p-4 rounded-lg shadow-sm border border-slate-200">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
                <Input 
                  placeholder="Search businesses..." 
                  className="pl-10" 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
              </div>
              <div className="flex-1 relative">
                <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
                <Input 
                  placeholder="Location..." 
                  className="pl-10" 
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
              </div>
              <Button 
                className="bg-[#007749] hover:bg-[#006739]"
                onClick={handleSearch}
              >
                Search
              </Button>
            </div>
          </div>
          
          {/* Results summary */}
          {!isLoading && businesses && (
            <div className="mb-6 flex items-center justify-between">
              <p className="text-slate-600">
                Found {businesses.length} businesses
                {appliedFilters.searchTerm && ` for "${appliedFilters.searchTerm}"`}
                {appliedFilters.location && ` in ${appliedFilters.location}`}
              </p>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </Button>
            </div>
          )}
          
          {/* Loading state */}
          {isLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-80"></div>
              ))}
            </div>
          )}

          {/* Error state */}
          {error && (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">Error loading businesses. Please try again.</p>
              <p className="text-sm text-slate-500 mb-4">
                This might be because there are no approved business listings in the database yet.
              </p>
              <div className="space-x-2">
                <Button onClick={() => window.location.reload()}>
                  Reload Page
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/test-seed'}>
                  Add Sample Data
                </Button>
              </div>
            </div>
          )}
          
          {/* Businesses list */}
          {!isLoading && !error && businesses && (
            <>
              {businesses.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {businesses.slice(0, 6).map((business) => (
                      <BusinessCard
                        key={business.id}
                        business={business}
                        featured={business.featured || false}
                      />
                    ))}
                  </div>

                  {/* Middle Ad Banner */}
                  {businesses.length > 6 && (
                    <AdBanner position="middle" className="my-8" />
                  )}

                  {/* Affiliate Links */}
                  <AffiliateLinks
                    context="business_listing"
                    category="business_services"
                    className="my-8"
                  />

                  {businesses.length > 6 && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
                      {businesses.slice(6).map((business) => (
                        <BusinessCard
                          key={business.id}
                          business={business}
                          featured={business.featured || false}
                        />
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-slate-600 mb-4">
                    No businesses found
                    {appliedFilters.searchTerm || appliedFilters.location
                      ? ' matching your criteria.'
                      : '. This might be because there are no approved business listings in the database yet.'
                    }
                  </p>
                  <div className="space-x-2">
                    {(appliedFilters.searchTerm || appliedFilters.location) ? (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSearchTerm('');
                          setLocation('');
                          setAppliedFilters({});
                        }}
                      >
                        Clear Filters
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={() => window.location.href = '/test-seed'}
                      >
                        Add Sample Data
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Bottom Ad Banner */}
          <AdBanner position="bottom" className="my-8" />

          {/* Pagination */}
          {!isLoading && businesses && businesses.length > 0 && (
            <div className="mt-10 flex justify-center">
              <nav className="flex items-center gap-1">
                <Button variant="outline" size="sm" disabled>Previous</Button>
                <Button variant="outline" size="sm" className="bg-[#003087] text-white">1</Button>
                <Button variant="outline" size="sm">2</Button>
                <Button variant="outline" size="sm">3</Button>
                <Button variant="outline" size="sm">Next</Button>
              </nav>
            </div>
          )}
            </div>

            {/* Sidebar */}
            <div className="hidden lg:block w-80">
              <div className="sticky top-4 space-y-6">
                <AdBanner position="sidebar" />
                <AffiliateLinks
                  context="business_listing"
                  category="business_services"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Businesses;
