import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Gift, 
  Star, 
  Award, 
  TrendingUp, 
  Trophy,
  Target,
  Zap,
  Crown
} from 'lucide-react';
import { toast } from 'sonner';

interface UserRewards {
  id: string;
  total_points: number;
  available_points: number;
  total_reviews: number;
  verified_reviews: number;
  helpful_reviews: number;
  reputation_score: number;
  badges: string[];
  level_name: string;
  level_number: number;
}

interface RewardRedemption {
  id: string;
  points_used: number;
  reward_type: string;
  reward_value: string;
  status: string;
  expires_at: string | null;
  created_at: string;
}

const UserRewardsDashboard = () => {
  const { user } = useAuth();
  const [selectedReward, setSelectedReward] = useState<string | null>(null);

  const { data: userRewards, isLoading, refetch } = useQuery({
    queryKey: ['user-rewards', user?.id],
    queryFn: async () => {
      if (!user) return null;

      const { data, error } = await supabase
        .from('user_rewards')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data as UserRewards | null;
    },
    enabled: !!user,
  });

  const { data: redemptions } = useQuery({
    queryKey: ['reward-redemptions', user?.id],
    queryFn: async () => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('reward_redemptions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data as RewardRedemption[];
    },
    enabled: !!user,
  });

  const availableRewards = [
    {
      id: 'discount_10',
      title: '10% Discount Code',
      description: 'Get 10% off at participating businesses',
      points: 100,
      type: 'discount_code',
      icon: <Gift className="h-6 w-6" />
    },
    {
      id: 'discount_20',
      title: '20% Discount Code',
      description: 'Get 20% off at participating businesses',
      points: 200,
      type: 'discount_code',
      icon: <Gift className="h-6 w-6" />
    },
    {
      id: 'premium_month',
      title: '1 Month Premium Features',
      description: 'Access premium features for 30 days',
      points: 500,
      type: 'premium_features',
      icon: <Crown className="h-6 w-6" />
    },
    {
      id: 'gift_voucher_50',
      title: 'R50 Gift Voucher',
      description: 'Redeemable at selected partner businesses',
      points: 1000,
      type: 'gift_voucher',
      icon: <Trophy className="h-6 w-6" />
    }
  ];

  const redeemReward = async (rewardId: string) => {
    if (!user || !userRewards) return;

    const reward = availableRewards.find(r => r.id === rewardId);
    if (!reward) return;

    if (userRewards.available_points < reward.points) {
      toast.error('Insufficient points for this reward');
      return;
    }

    try {
      // Generate reward code
      const rewardCode = `SA360-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      
      const { error } = await supabase
        .from('reward_redemptions')
        .insert({
          user_id: user.id,
          points_used: reward.points,
          reward_type: reward.type,
          reward_value: rewardCode,
          expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days
          status: 'active'
        });

      if (error) throw error;

      // Update user points
      await supabase
        .from('user_rewards')
        .update({
          available_points: userRewards.available_points - reward.points,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id);

      toast.success(`Reward redeemed! Your code: ${rewardCode}`, {
        description: 'Check your redemptions below for details.',
      });

      refetch();
    } catch (error) {
      console.error('Error redeeming reward:', error);
      toast.error('Failed to redeem reward. Please try again.');
    }
  };

  const getLevelProgress = () => {
    if (!userRewards) return 0;
    
    const levelThresholds = [0, 50, 150, 300, 500, 1000]; // Points needed for each level
    const currentLevel = userRewards.level_number;
    const nextLevelThreshold = levelThresholds[currentLevel] || levelThresholds[levelThresholds.length - 1];
    const currentLevelThreshold = levelThresholds[currentLevel - 1] || 0;
    
    const progress = ((userRewards.total_points - currentLevelThreshold) / (nextLevelThreshold - currentLevelThreshold)) * 100;
    return Math.min(100, Math.max(0, progress));
  };

  const getNextLevelName = () => {
    const levels = ['Newcomer', 'Reviewer', 'Expert Reviewer', 'Review Master', 'Review Legend'];
    return levels[userRewards?.level_number || 0] || 'Max Level';
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Gift className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to view your rewards and points</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your rewards...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* User Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-[#007749] mb-1">
              {userRewards?.available_points || 0}
            </div>
            <div className="text-sm text-slate-600">Available Points</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {userRewards?.total_reviews || 0}
            </div>
            <div className="text-sm text-slate-600">Reviews Written</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {userRewards?.reputation_score.toFixed(0) || 0}
            </div>
            <div className="text-sm text-slate-600">Reputation Score</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600 mb-1">
              {userRewards?.level_number || 1}
            </div>
            <div className="text-sm text-slate-600">Level</div>
          </CardContent>
        </Card>
      </div>

      {/* Level Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-[#007749]" />
            {userRewards?.level_name || 'Newcomer'}
          </CardTitle>
          <CardDescription>
            Progress to {getNextLevelName()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Progress value={getLevelProgress()} className="h-3" />
            <div className="flex justify-between text-sm text-slate-600">
              <span>{userRewards?.total_points || 0} points</span>
              <span>Next level at {userRewards?.level_number === 5 ? 'Max' : '1000'} points</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Available Rewards */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5 text-[#007749]" />
            Redeem Rewards
          </CardTitle>
          <CardDescription>
            Use your points to unlock exclusive rewards and discounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {availableRewards.map((reward) => (
              <Card key={reward.id} className="relative">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-[#007749] bg-opacity-10 rounded-lg text-[#007749]">
                        {reward.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold">{reward.title}</h3>
                        <p className="text-sm text-slate-600">{reward.description}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge className="bg-[#007749] text-white">
                      {reward.points} points
                    </Badge>
                    <Button
                      size="sm"
                      onClick={() => redeemReward(reward.id)}
                      disabled={!userRewards || userRewards.available_points < reward.points}
                      className="bg-[#007749] hover:bg-[#006739]"
                    >
                      Redeem
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Redemptions */}
      {redemptions && redemptions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-[#007749]" />
              Recent Redemptions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {redemptions.map((redemption) => (
                <div key={redemption.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div>
                    <div className="font-medium">{redemption.reward_type.replace('_', ' ')}</div>
                    <div className="text-sm text-slate-600">
                      Code: {redemption.reward_value}
                    </div>
                    <div className="text-xs text-slate-500">
                      {new Date(redemption.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge 
                      className={
                        redemption.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }
                    >
                      {redemption.status}
                    </Badge>
                    <div className="text-sm text-slate-600 mt-1">
                      -{redemption.points_used} points
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserRewardsDashboard;
