-- Create affiliate partners table
CREATE TABLE public.affiliate_partners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  website_url TEXT NOT NULL,
  category TEXT NOT NULL, -- 'insurance', 'banking', 'business_services', 'marketing', etc.
  commission_rate DECIMAL(5,2) DEFAULT 0.00, -- Percentage commission
  tracking_code TEXT, -- Unique tracking identifier
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
  contact_email TEXT,
  contact_phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create affiliate links table
CREATE TABLE public.affiliate_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES public.affiliate_partners(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  target_url TEXT NOT NULL,
  affiliate_url TEXT NOT NULL, -- URL with tracking parameters
  category TEXT NOT NULL,
  placement_context TEXT, -- 'business_listing', 'category_page', 'homepage', etc.
  priority INTEGER DEFAULT 0, -- Higher priority = more prominent placement
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create affiliate clicks tracking table
CREATE TABLE public.affiliate_clicks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  link_id UUID REFERENCES public.affiliate_links(id) ON DELETE CASCADE,
  business_id UUID REFERENCES public.business_listings(id) ON DELETE SET NULL,
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  conversion_tracked BOOLEAN DEFAULT FALSE,
  conversion_value DECIMAL(10,2), -- Value of conversion if tracked
  commission_earned DECIMAL(10,2) -- Commission earned from this click
);

-- Create service providers table for marketplace
CREATE TABLE public.service_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  business_name TEXT NOT NULL,
  service_category TEXT NOT NULL, -- 'web_development', 'photography', 'seo', 'marketing', etc.
  description TEXT NOT NULL,
  specialties TEXT[], -- Array of specialties
  portfolio_url TEXT,
  pricing_model TEXT, -- 'hourly', 'project', 'retainer', 'commission'
  price_range TEXT,
  location TEXT,
  contact_email TEXT NOT NULL,
  contact_phone TEXT,
  website_url TEXT,
  logo_url TEXT,
  images TEXT[],
  rating DECIMAL(3,2) DEFAULT 0.00,
  review_count INTEGER DEFAULT 0,
  verified BOOLEAN DEFAULT FALSE,
  featured BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'suspended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service requests table
CREATE TABLE public.service_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  service_category TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  budget_range TEXT,
  timeline TEXT,
  requirements JSONB, -- Structured requirements data
  contact_name TEXT NOT NULL,
  contact_email TEXT NOT NULL,
  contact_phone TEXT,
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service proposals table
CREATE TABLE public.service_proposals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id UUID REFERENCES public.service_requests(id) ON DELETE CASCADE,
  provider_id UUID REFERENCES public.service_providers(id) ON DELETE CASCADE,
  proposal_text TEXT NOT NULL,
  estimated_cost DECIMAL(10,2),
  estimated_timeline TEXT,
  portfolio_samples TEXT[], -- URLs to relevant work samples
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service reviews table
CREATE TABLE public.service_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID REFERENCES public.service_providers(id) ON DELETE CASCADE,
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  request_id UUID REFERENCES public.service_requests(id) ON DELETE SET NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample affiliate partners
INSERT INTO public.affiliate_partners (name, description, website_url, category, commission_rate, tracking_code, contact_email) VALUES
('FNB Business Banking', 'Comprehensive business banking solutions for South African businesses', 'https://www.fnb.co.za/business', 'banking', 5.00, 'FNB_BIZ_001', '<EMAIL>'),
('Santam Business Insurance', 'Protect your business with comprehensive insurance coverage', 'https://www.santam.co.za/business', 'insurance', 8.00, 'SANTAM_BIZ_001', '<EMAIL>'),
('Xero Accounting Software', 'Cloud-based accounting software for small businesses', 'https://www.xero.com/za/', 'business_services', 15.00, 'XERO_ZA_001', '<EMAIL>'),
('Google Workspace', 'Professional email and productivity tools for businesses', 'https://workspace.google.com/', 'business_services', 10.00, 'GOOGLE_WS_001', '<EMAIL>');

-- Insert sample affiliate links
INSERT INTO public.affiliate_links (partner_id, title, description, target_url, affiliate_url, category, placement_context, priority) VALUES
((SELECT id FROM public.affiliate_partners WHERE tracking_code = 'FNB_BIZ_001'), 'Open a Business Account', 'Get started with FNB Business Banking', 'https://www.fnb.co.za/business/accounts', 'https://www.fnb.co.za/business/accounts?ref=SA360_BIZ', 'banking', 'business_listing', 10),
((SELECT id FROM public.affiliate_partners WHERE tracking_code = 'SANTAM_BIZ_001'), 'Protect Your Business', 'Get a quote for business insurance', 'https://www.santam.co.za/business/quote', 'https://www.santam.co.za/business/quote?ref=SA360_INS', 'insurance', 'business_listing', 8),
((SELECT id FROM public.affiliate_partners WHERE tracking_code = 'XERO_ZA_001'), 'Try Xero Free', '30-day free trial of Xero accounting software', 'https://www.xero.com/za/signup', 'https://www.xero.com/za/signup?ref=SA360_ACC', 'business_services', 'category_page', 9),
((SELECT id FROM public.affiliate_partners WHERE tracking_code = 'GOOGLE_WS_001'), 'Get Google Workspace', 'Professional email for your business', 'https://workspace.google.com/pricing', 'https://workspace.google.com/pricing?ref=SA360_WS', 'business_services', 'homepage', 7);

-- Add RLS policies
ALTER TABLE public.affiliate_partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.affiliate_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.affiliate_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_proposals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_reviews ENABLE ROW LEVEL SECURITY;

-- Public read access for active affiliate content
CREATE POLICY "Anyone can view active affiliate partners" ON public.affiliate_partners
  FOR SELECT USING (status = 'active');

CREATE POLICY "Anyone can view active affiliate links" ON public.affiliate_links
  FOR SELECT USING (status = 'active');

-- Anyone can track affiliate clicks
CREATE POLICY "Anyone can track affiliate clicks" ON public.affiliate_clicks
  FOR INSERT WITH CHECK (true);

-- Service providers policies
CREATE POLICY "Anyone can view approved service providers" ON public.service_providers
  FOR SELECT USING (status = 'approved');

CREATE POLICY "Users can create service provider profiles" ON public.service_providers
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their service provider profiles" ON public.service_providers
  FOR UPDATE USING (auth.uid() = user_id);

-- Service requests policies
CREATE POLICY "Anyone can view open service requests" ON public.service_requests
  FOR SELECT USING (status = 'open');

CREATE POLICY "Business owners can create service requests" ON public.service_requests
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Business owners can update their service requests" ON public.service_requests
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Service proposals policies
CREATE POLICY "Service providers can create proposals" ON public.service_proposals
  FOR INSERT WITH CHECK (
    provider_id IN (
      SELECT id FROM public.service_providers 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Relevant parties can view proposals" ON public.service_proposals
  FOR SELECT USING (
    provider_id IN (
      SELECT id FROM public.service_providers 
      WHERE user_id = auth.uid()
    )
    OR
    request_id IN (
      SELECT sr.id FROM public.service_requests sr
      JOIN public.business_listings bl ON sr.business_id = bl.id
      WHERE bl.user_id = auth.uid()
    )
  );

-- Service reviews policies
CREATE POLICY "Anyone can view service reviews" ON public.service_reviews
  FOR SELECT USING (true);

CREATE POLICY "Business owners can create reviews" ON public.service_reviews
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_affiliate_links_category ON public.affiliate_links(category);
CREATE INDEX idx_affiliate_links_placement ON public.affiliate_links(placement_context);
CREATE INDEX idx_affiliate_clicks_link_id ON public.affiliate_clicks(link_id);
CREATE INDEX idx_service_providers_category ON public.service_providers(service_category);
CREATE INDEX idx_service_requests_category ON public.service_requests(service_category);
CREATE INDEX idx_service_requests_status ON public.service_requests(status);
