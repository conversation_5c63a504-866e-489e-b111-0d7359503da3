
project_id = "tcqltnxugtzludqhngrs"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54324
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://tcqltnxugtzludqhngrs.supabase.co"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
refresh_token_reuse_interval = 10

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[db]
port = 54322

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
port = 54323
tenant_id = "realtime-dev"

[studio]
enabled = true
port = 54323
api_url = "http://localhost:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
port = 54321
file_size_limit = "50MiB"
image_transformation = true

[analytics]
enabled = false
port = 54327
vector_port = 54328
gw_port = 54329

[functions.create-payfast-subscription]
verify_jwt = true

[functions.payfast-webhook]
verify_jwt = false

[functions.create-paypal-subscription]
verify_jwt = true
