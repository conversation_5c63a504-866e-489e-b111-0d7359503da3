import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Quote } from "lucide-react";

type Testimonial = {
  id: string;
  name: string;
  role: string;
  company: string;
  quote: string;
  rating: number;
};

const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    role: "Owner",
    company: "Cape Town Tours",
    quote: "Listing our tour company on SA360 has been a game-changer. We've seen a 40% increase in bookings since joining the platform. The visibility and credibility it gives our business is invaluable.",
    rating: 5,
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    role: "Manager",
    company: "Durban Beachfront Hotel",
    quote: "The return on investment has been incredible. Within just three months of listing our hotel, we've received numerous bookings directly from SA360 visitors. The platform is user-friendly and the support team is always helpful.",
    rating: 5,
  },
  {
    id: "3",
    name: "<PERSON>",
    role: "Proprietor",
    company: "Stellenbosch Wine Estate",
    quote: "As a premium wine estate, we're selective about our marketing channels. SA360 has proven to be the perfect platform to reach quality customers who appreciate our offerings. The detailed business profile allows us to showcase what makes us special.",
    rating: 4,
  },
];

const TestimonialsSection = () => {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-slate-800 mb-4">
            What Our Business Owners Say
          </h2>
          <p className="text-slate-600 max-w-2xl mx-auto">
            Discover how SA360 has helped businesses across South Africa increase their visibility and connect with new customers
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} className="border border-slate-200 shadow-md h-full">
              <CardContent className="p-6 flex flex-col h-full">
                <div className="mb-4 text-[#007749]">
                  <Quote className="h-8 w-8" />
                </div>
                <p className="text-slate-700 mb-6 flex-grow italic">
                  "{testimonial.quote}"
                </p>
                <div className="mt-auto">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-semibold text-slate-800">{testimonial.name}</h4>
                      <p className="text-sm text-slate-600">
                        {testimonial.role}, {testimonial.company}
                      </p>
                    </div>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`h-5 w-5 ${
                            i < testimonial.rating ? "text-[#FDB913]" : "text-slate-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 15.934l-6.18 3.254 1.18-6.875L.083 7.62l6.9-1.004L10 .5l3.017 6.116 6.9 1.004-4.917 4.693 1.18 6.875L10 15.934z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
