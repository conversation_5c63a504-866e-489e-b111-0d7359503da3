import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Phone, Mail, MessageSquare } from 'lucide-react';

interface LeadGenerationFormProps {
  businessId: string;
  businessName: string;
  industry: string;
  onSuccess?: () => void;
}

const LeadGenerationForm = ({ businessId, businessName, industry, onSuccess }: LeadGenerationFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    serviceType: '',
    message: '',
    budgetRange: '',
    urgency: 'medium'
  });

  const getIndustrySpecificFields = (industry: string) => {
    const commonServiceTypes = {
      'legal': ['Legal Consultation', 'Contract Review', 'Litigation', 'Corporate Law', 'Family Law', 'Property Law'],
      'medical': ['General Consultation', 'Specialist Referral', 'Health Screening', 'Emergency Care', 'Preventive Care'],
      'real-estate': ['Property Valuation', 'Buying Assistance', 'Selling Assistance', 'Rental Management', 'Investment Advice'],
      'automotive': ['Vehicle Repair', 'Maintenance Service', 'Parts Replacement', 'Diagnostic Service', 'Emergency Roadside'],
      'technology': ['Software Development', 'IT Support', 'System Integration', 'Cybersecurity', 'Cloud Services'],
      'professional-services': ['Business Consulting', 'Financial Planning', 'Tax Services', 'Audit Services', 'Strategic Planning'],
      'default': ['General Inquiry', 'Service Request', 'Quote Request', 'Consultation', 'Support']
    };

    return commonServiceTypes[industry as keyof typeof commonServiceTypes] || commonServiceTypes.default;
  };

  const budgetRanges = [
    'Under R1,000',
    'R1,000 - R5,000',
    'R5,000 - R10,000',
    'R10,000 - R25,000',
    'R25,000 - R50,000',
    'R50,000+',
    'Prefer not to say'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('leads')
        .insert({
          business_id: businessId,
          customer_name: formData.customerName,
          customer_email: formData.customerEmail,
          customer_phone: formData.customerPhone || null,
          industry: industry,
          service_type: formData.serviceType,
          message: formData.message || null,
          budget_range: formData.budgetRange || null,
          urgency: formData.urgency,
          source: 'directory'
        });

      if (error) throw error;

      toast.success('Your inquiry has been sent successfully! The business will contact you soon.');
      
      // Reset form
      setFormData({
        customerName: '',
        customerEmail: '',
        customerPhone: '',
        serviceType: '',
        message: '',
        budgetRange: '',
        urgency: 'medium'
      });

      onSuccess?.();
    } catch (error) {
      console.error('Error submitting lead:', error);
      toast.error('Failed to send inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const serviceTypes = getIndustrySpecificFields(industry);

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-[#007749]" />
          Contact {businessName}
        </CardTitle>
        <CardDescription>
          Get a personalized quote or consultation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="customerName">Full Name *</Label>
            <Input
              id="customerName"
              value={formData.customerName}
              onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
              required
              placeholder="Your full name"
            />
          </div>

          <div>
            <Label htmlFor="customerEmail">Email Address *</Label>
            <Input
              id="customerEmail"
              type="email"
              value={formData.customerEmail}
              onChange={(e) => setFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
              required
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <Label htmlFor="customerPhone">Phone Number</Label>
            <Input
              id="customerPhone"
              type="tel"
              value={formData.customerPhone}
              onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
              placeholder="+27 XX XXX XXXX"
            />
          </div>

          <div>
            <Label htmlFor="serviceType">Service Needed *</Label>
            <Select value={formData.serviceType} onValueChange={(value) => setFormData(prev => ({ ...prev, serviceType: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select service type" />
              </SelectTrigger>
              <SelectContent>
                {serviceTypes.map((service) => (
                  <SelectItem key={service} value={service}>
                    {service}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="budgetRange">Budget Range</Label>
            <Select value={formData.budgetRange} onValueChange={(value) => setFormData(prev => ({ ...prev, budgetRange: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select budget range" />
              </SelectTrigger>
              <SelectContent>
                {budgetRanges.map((range) => (
                  <SelectItem key={range} value={range}>
                    {range}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="urgency">Urgency</Label>
            <Select value={formData.urgency} onValueChange={(value) => setFormData(prev => ({ ...prev, urgency: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low - Within a month</SelectItem>
                <SelectItem value="medium">Medium - Within a week</SelectItem>
                <SelectItem value="high">High - ASAP</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="message">Additional Details</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              placeholder="Please provide more details about your requirements..."
              rows={3}
            />
          </div>

          <Button 
            type="submit" 
            className="w-full bg-[#007749] hover:bg-[#006739]"
            disabled={isSubmitting || !formData.customerName || !formData.customerEmail || !formData.serviceType}
          >
            {isSubmitting ? 'Sending...' : 'Send Inquiry'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default LeadGenerationForm;
