import React from "react";
import Layout from "@/components/layout/Layout";
import SEOHead from "@/components/seo/SEOHead";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, MapPin, Star } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Import the category data
type Business = {
  id: string;
  name: string;
  category: string;
  location: string;
  rating: number;
  reviews: number;
  image: string;
  isPremium: boolean;
  description: string;
};

type Category = {
  id: string;
  name: string;
  icon: string;
  count: number;
  description: string;
};

const allCategories: Category[] = [
  { id: "restaurants", name: "Restaurants", icon: "🍽️", count: 1250, description: "Find the best dining experiences across South Africa" },
  { id: "accommodation", name: "Accommodation", icon: "🏨", count: 876, description: "Hotels, guesthouses, and B&Bs for every budget" },
  { id: "retail", name: "Retail Stores", icon: "🛒", count: 1843, description: "Shopping destinations and retail outlets" },
  { id: "health", name: "Healthcare", icon: "🏥", count: 643, description: "Medical centers, clinics and healthcare providers" },
  { id: "automotive", name: "Automotive", icon: "🚗", count: 925, description: "Car dealerships, mechanics and auto services" },
  { id: "education", name: "Education", icon: "🎓", count: 487, description: "Schools, universities, and learning centers" },
  { id: "finance", name: "Financial Services", icon: "💰", count: 372, description: "Banks, insurance, and financial advisors" },
  { id: "technology", name: "Technology", icon: "💻", count: 564, description: "IT companies, tech retailers and service providers" },
  { id: "professional", name: "Professional Services", icon: "👔", count: 735, description: "Legal, accounting, consulting and more" },
  { id: "beauty", name: "Beauty & Wellness", icon: "💇‍♀️", count: 892, description: "Salons, spas, and wellness centers" },
  { id: "construction", name: "Construction", icon: "🏗️", count: 456, description: "Builders, contractors, and construction suppliers" },
  { id: "entertainment", name: "Entertainment", icon: "🎭", count: 623, description: "Theaters, venues, and entertainment centers" },
];

// Sample businesses for the category
const mockBusinessesByCategory: Record<string, Business[]> = {
  "restaurants": [
    {
      id: "1",
      name: "Ocean Basket",
      category: "Restaurants",
      location: "Cape Town, Western Cape",
      rating: 4.7,
      reviews: 128,
      image: "https://images.unsplash.com/photo-**********-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
      isPremium: true,
      description: "Seafood restaurant chain offering a variety of fish dishes and platters."
    },
    {
      id: "7",
      name: "Nando's",
      category: "Restaurants",
      location: "Johannesburg, Gauteng",
      rating: 4.5,
      reviews: 215,
      image: "https://images.unsplash.com/photo-1555992336-fb0d29498b13?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
      isPremium: false,
      description: "Portuguese-African chicken restaurant known for peri-peri flavors."
    },
    {
      id: "8",
      name: "Hussar Grill",
      category: "Restaurants",
      location: "Stellenbosch, Western Cape",
      rating: 4.8,
      reviews: 183,
      image: "https://images.unsplash.com/photo-1514326640560-7d063ef2aed5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
      isPremium: true,
      description: "Premium steakhouse with aged beef and fine wines."
    }
  ],
  "accommodation": [
    {
      id: "2",
      name: "The Capital Hotels",
      category: "Accommodation",
      location: "Sandton, Gauteng",
      rating: 4.9,
      reviews: 213,
      image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
      isPremium: true,
      description: "Luxury apartments and hotel rooms in the heart of Sandton's business district."
    }
  ]
};

// Default fallback businesses
const defaultBusinesses: Business[] = [
  {
    id: "3",
    name: "Woolworths",
    category: "Retail",
    location: "Nationwide",
    rating: 4.5,
    reviews: 876,
    image: "https://images.unsplash.com/photo-1604719312566-8912e9227c6a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
    isPremium: false,
    description: "Quality clothing, food, and home products with stores across South Africa."
  },
  {
    id: "4",
    name: "FNB Business",
    category: "Financial Services",
    location: "Johannesburg, Gauteng",
    rating: 4.3,
    reviews: 542,
    image: "https://images.unsplash.com/photo-*************-7e09a677fb2c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    isPremium: true,
    description: "Comprehensive banking services for businesses of all sizes."
  }
];

const CategoryDetail = () => {
  const { categoryId } = useParams<{ categoryId: string }>();
  
  // Find the category
  const category = allCategories.find(cat => cat.id === categoryId);
  
  // Get businesses for this category or use default businesses if none found
  const businesses = mockBusinessesByCategory[categoryId || ""] || defaultBusinesses;

  if (!category) {
    return (
      <>
        <SEOHead
          title="Category Not Found - SA360"
          description="The business category you're looking for could not be found."
          noindex={true}
        />
        <Layout>
          <div className="py-12 text-center">
            <h1 className="text-3xl font-bold mb-4">Category Not Found</h1>
            <p className="mb-6">We couldn't find the category you're looking for.</p>
            <Button asChild>
              <Link to="/categories">Back to Categories</Link>
            </Button>
          </div>
        </Layout>
      </>
    );
  }

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": `${category.name} in South Africa`,
    "description": category.description,
    "url": `https://sa360.co.za/category/${categoryId}`,
    "mainEntity": {
      "@type": "ItemList",
      "name": `${category.name} Businesses`,
      "numberOfItems": businesses.length,
      "itemListElement": businesses.map((business, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "LocalBusiness",
          "name": business.name,
          "description": business.description,
          "image": business.image,
          "address": {
            "@type": "PostalAddress",
            "addressLocality": business.location.split(',')[0],
            "addressRegion": business.location.split(',')[1]?.trim(),
            "addressCountry": "ZA"
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": business.rating,
            "reviewCount": business.reviews
          }
        }
      }))
    }
  };

  return (
    <>
      <SEOHead
        title={`${category.name} in South Africa - Find the Best ${category.name}`}
        description={`Discover the best ${category.name.toLowerCase()} in South Africa. ${category.description} Browse verified listings with reviews, ratings, and contact details.`}
        keywords={`${category.name} South Africa, best ${category.name.toLowerCase()} SA, ${category.name.toLowerCase()} directory, South African ${category.name.toLowerCase()}`}
        structuredData={structuredData}
      />
      <Layout>
        <div className="py-10">
          <div className="container mx-auto px-4">
            <header className="mb-8">
              <h1 className="text-3xl md:text-4xl font-bold mb-2 text-slate-800 flex items-center gap-3">
                <span className="text-4xl" role="img" aria-label={category.name}>
                  {category.icon}
                </span> 
                {category.name} in South Africa
              </h1>
              <p className="text-slate-600 text-lg">{category.description}</p>
              <p className="text-sm text-slate-500 mt-2">
                Showing {businesses.length} verified {category.name.toLowerCase()} listings
              </p>
            </header>
            
            {/* Search and filter section */}
            <section className="mb-8 bg-white p-4 rounded-lg shadow-sm border border-slate-200">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
                  <Input placeholder={`Search ${category.name.toLowerCase()}...`} className="pl-10" />
                </div>
                <div className="flex-1 relative">
                  <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
                  <Input placeholder="Location (city, province)..." className="pl-10" />
                </div>
                <Button className="bg-[#007749] hover:bg-[#006739]">Search</Button>
              </div>
            </section>
            
            {/* Businesses list */}
            <section>
              <h2 className="text-2xl font-semibold mb-6 text-slate-800">
                Featured {category.name}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {businesses.map((business) => (
                  <article key={business.id} className="bg-white border border-slate-200 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                    <div className="aspect-[16/9] relative">
                      <img 
                        src={business.image} 
                        alt={`${business.name} - ${category.name} in ${business.location}`}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                      {business.isPremium && (
                        <Badge className="absolute top-2 right-2 bg-[#FDB913] text-black">
                          Premium
                        </Badge>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg text-slate-800 mb-1">
                        {business.name}
                      </h3>
                      <p className="text-sm text-slate-500 mb-2">
                        {business.category} • {business.location}
                      </p>
                      <p className="text-sm text-slate-600 mb-3">
                        {business.description}
                      </p>
                      <div className="flex items-center">
                        <div className="flex items-center text-amber-500">
                          <Star className="h-4 w-4 fill-current" />
                          <span className="ml-1 text-sm font-medium">{business.rating}</span>
                        </div>
                        <span className="mx-2 text-slate-300">•</span>
                        <span className="text-sm text-slate-500">{business.reviews} reviews</span>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </section>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default CategoryDetail;
