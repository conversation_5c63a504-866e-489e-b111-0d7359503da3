import React from 'react';
import Layout from '@/components/layout/Layout';
import AdPerformanceDashboard from '@/components/ads/AdPerformanceDashboard';
import CampaignManager from '@/components/ads/CampaignManager';
import AdSubmissionForm from '@/components/ads/AdSubmissionForm';
import RealTimeAnalytics from '@/components/ads/RealTimeAnalytics';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Target, 
  PlusCircle, 
  TrendingUp, 
  Eye, 
  MousePointer,
  DollarSign,
  Users,
  Zap,
  Shield,
  Globe,
  Smartphone
} from 'lucide-react';

const Advertising = () => {
  const adFeatures = [
    {
      icon: <Target className="h-8 w-8" />,
      title: 'Precise Targeting',
      description: 'Reach your ideal customers with location, age, and interest-based targeting',
      color: 'text-blue-600'
    },
    {
      icon: <BarChart3 className="h-8 w-8" />,
      title: 'Real-time Analytics',
      description: 'Track impressions, clicks, and conversions with detailed performance metrics',
      color: 'text-green-600'
    },
    {
      icon: <DollarSign className="h-8 w-8" />,
      title: 'Budget Control',
      description: 'Set daily and total budgets with automatic spending controls',
      color: 'text-purple-600'
    },
    {
      icon: <Smartphone className="h-8 w-8" />,
      title: 'Multi-device Reach',
      description: 'Your ads display perfectly on desktop, mobile, and tablet devices',
      color: 'text-orange-600'
    }
  ];

  const adPlacements = [
    {
      position: 'Top Banner',
      description: 'Prime visibility at the top of pages',
      price: 'R2.50/click',
      impressions: '50K+/month',
      ctr: '3.2%'
    },
    {
      position: 'Sidebar',
      description: 'Persistent visibility on business listings',
      price: 'R1.80/click',
      impressions: '35K+/month',
      ctr: '2.8%'
    },
    {
      position: 'Middle Content',
      description: 'Integrated within search results',
      price: 'R2.20/click',
      impressions: '40K+/month',
      ctr: '3.5%'
    },
    {
      position: 'Bottom Banner',
      description: 'Cost-effective placement at page bottom',
      price: 'R1.50/click',
      impressions: '30K+/month',
      ctr: '2.1%'
    }
  ];

  const successStats = [
    {
      icon: <Eye className="h-6 w-6" />,
      value: '2.5M+',
      label: 'Monthly Impressions',
      color: 'text-blue-600'
    },
    {
      icon: <MousePointer className="h-6 w-6" />,
      value: '85K+',
      label: 'Monthly Clicks',
      color: 'text-green-600'
    },
    {
      icon: <Users className="h-6 w-6" />,
      value: '500+',
      label: 'Active Advertisers',
      color: 'text-purple-600'
    },
    {
      icon: <TrendingUp className="h-6 w-6" />,
      value: '3.4%',
      label: 'Average CTR',
      color: 'text-orange-600'
    }
  ];

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-slate-800">
              Business Advertising Platform
            </h1>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Reach thousands of potential customers across South Africa with targeted advertising campaigns. 
              Track performance, optimize spending, and grow your business.
            </p>
          </div>

          {/* Success Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-10">
            {successStats.map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center">
                  <div className={`p-3 rounded-lg w-fit mx-auto mb-4 ${stat.color} bg-opacity-10`}>
                    <div className={stat.color}>
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-slate-800 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-sm text-slate-600">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Tabs defaultValue="dashboard" className="space-y-8">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="realtime">Live Analytics</TabsTrigger>
              <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
              <TabsTrigger value="create">Create Ad</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard">
              <AdPerformanceDashboard />
            </TabsContent>

            <TabsContent value="realtime">
              <RealTimeAnalytics />
            </TabsContent>

            <TabsContent value="campaigns">
              <CampaignManager />
            </TabsContent>

            <TabsContent value="create">
              <AdSubmissionForm />
            </TabsContent>

            <TabsContent value="pricing">
              <div className="space-y-8">
                {/* Features Section */}
                <div>
                  <h2 className="text-2xl font-bold text-center mb-8">Why Advertise With Us?</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {adFeatures.map((feature, index) => (
                      <Card key={index} className="text-center">
                        <CardContent className="p-6">
                          <div className={`p-3 rounded-lg w-fit mx-auto mb-4 ${feature.color} bg-opacity-10`}>
                            <div className={feature.color}>
                              {feature.icon}
                            </div>
                          </div>
                          <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                          <p className="text-slate-600 text-sm">{feature.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Placement Options */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5 text-[#007749]" />
                      Ad Placement Options
                    </CardTitle>
                    <CardDescription>
                      Choose the best placement for your advertising goals
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {adPlacements.map((placement, index) => (
                        <Card key={index} className="border-2 hover:border-[#007749] transition-colors">
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between mb-4">
                              <h3 className="font-semibold text-lg">{placement.position}</h3>
                              <Badge className="bg-[#007749] text-white">
                                {placement.price}
                              </Badge>
                            </div>
                            <p className="text-slate-600 mb-4">{placement.description}</p>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="text-slate-500">Monthly Impressions</p>
                                <p className="font-semibold">{placement.impressions}</p>
                              </div>
                              <div>
                                <p className="text-slate-500">Average CTR</p>
                                <p className="font-semibold">{placement.ctr}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Pricing Tiers */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-[#007749]" />
                      Advertising Packages
                    </CardTitle>
                    <CardDescription>
                      Flexible pricing options to suit businesses of all sizes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Card className="border-2">
                        <CardContent className="p-6 text-center">
                          <h3 className="font-semibold text-xl mb-2">Starter</h3>
                          <div className="text-3xl font-bold text-[#007749] mb-4">R500</div>
                          <p className="text-slate-600 mb-6">Perfect for small businesses</p>
                          <ul className="text-sm text-left space-y-2 mb-6">
                            <li>• R500 ad credit</li>
                            <li>• Basic targeting options</li>
                            <li>• Standard analytics</li>
                            <li>• Email support</li>
                          </ul>
                          <button className="w-full bg-[#007749] text-white py-2 px-4 rounded-lg hover:bg-[#006739] transition-colors">
                            Get Started
                          </button>
                        </CardContent>
                      </Card>

                      <Card className="border-2 border-[#007749] relative">
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-[#FDB913] text-black">Most Popular</Badge>
                        </div>
                        <CardContent className="p-6 text-center">
                          <h3 className="font-semibold text-xl mb-2">Professional</h3>
                          <div className="text-3xl font-bold text-[#007749] mb-4">R2,000</div>
                          <p className="text-slate-600 mb-6">Ideal for growing businesses</p>
                          <ul className="text-sm text-left space-y-2 mb-6">
                            <li>• R2,000 ad credit</li>
                            <li>• Advanced targeting</li>
                            <li>• Detailed analytics</li>
                            <li>• Priority support</li>
                            <li>• A/B testing tools</li>
                          </ul>
                          <button className="w-full bg-[#007749] text-white py-2 px-4 rounded-lg hover:bg-[#006739] transition-colors">
                            Get Started
                          </button>
                        </CardContent>
                      </Card>

                      <Card className="border-2">
                        <CardContent className="p-6 text-center">
                          <h3 className="font-semibold text-xl mb-2">Enterprise</h3>
                          <div className="text-3xl font-bold text-[#007749] mb-4">R5,000+</div>
                          <p className="text-slate-600 mb-6">For large-scale campaigns</p>
                          <ul className="text-sm text-left space-y-2 mb-6">
                            <li>• Custom ad credit</li>
                            <li>• Premium targeting</li>
                            <li>• Advanced analytics</li>
                            <li>• Dedicated account manager</li>
                            <li>• Custom reporting</li>
                          </ul>
                          <button className="w-full bg-[#007749] text-white py-2 px-4 rounded-lg hover:bg-[#006739] transition-colors">
                            Contact Sales
                          </button>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>

                {/* Trust Indicators */}
                <Card className="bg-gradient-to-r from-[#007749] to-[#006739] text-white">
                  <CardContent className="p-8 text-center">
                    <div className="flex items-center justify-center gap-2 mb-4">
                      <Shield className="h-6 w-6" />
                      <h3 className="text-2xl font-bold">Trusted by 500+ South African Businesses</h3>
                    </div>
                    <p className="text-lg mb-6 opacity-90">
                      Join successful businesses that have grown their customer base through our advertising platform
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold mb-2">
                          <Zap className="h-8 w-8 mx-auto mb-2" />
                          24/7
                        </div>
                        <div className="text-sm opacity-80">Campaign Monitoring</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold mb-2">
                          <Shield className="h-8 w-8 mx-auto mb-2" />
                          100%
                        </div>
                        <div className="text-sm opacity-80">Fraud Protection</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold mb-2">
                          <Users className="h-8 w-8 mx-auto mb-2" />
                          98%
                        </div>
                        <div className="text-sm opacity-80">Customer Satisfaction</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Advertising;
