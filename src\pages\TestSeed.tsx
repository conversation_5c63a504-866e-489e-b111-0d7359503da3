import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { seedSampleBusinesses } from '@/utils/seedBusinesses';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

const TestSeed = () => {
  const [isSeeding, setIsSeeding] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  const handleSeed = async () => {
    setIsSeeding(true);
    try {
      await seedSampleBusinesses();
      toast.success('Sample businesses seeded successfully!');
    } catch (error) {
      console.error('Error seeding:', error);
      toast.error('Error seeding businesses');
    } finally {
      setIsSeeding(false);
    }
  };

  const testConnection = async () => {
    setIsTesting(true);
    try {
      console.log('Testing database connection...');

      // Test categories table
      const { data: categories, error: catError } = await supabase
        .from('categories')
        .select('*')
        .limit(5);

      if (catError) {
        console.error('Categories error:', catError);
        toast.error(`Categories error: ${catError.message}`);
        return;
      }

      console.log('Categories:', categories);

      // Test business_listings table - try different queries
      console.log('Testing business_listings table...');

      // First try to get all businesses (might be blocked by RLS)
      const { data: allBusinesses, error: allBizError } = await supabase
        .from('business_listings')
        .select('*')
        .limit(5);

      console.log('All businesses query:', { data: allBusinesses, error: allBizError });

      // Try to get only approved businesses
      const { data: approvedBusinesses, error: approvedError } = await supabase
        .from('business_listings')
        .select('*')
        .eq('status', 'approved')
        .limit(5);

      console.log('Approved businesses query:', { data: approvedBusinesses, error: approvedError });

      // Try to get count
      const { count, error: countError } = await supabase
        .from('business_listings')
        .select('*', { count: 'exact', head: true });

      console.log('Count query:', { count, error: countError });

      const businessCount = approvedBusinesses?.length || 0;
      const totalCount = count || 0;

      toast.success(`Connection OK! Found ${categories?.length || 0} categories, ${businessCount} approved businesses, ${totalCount} total businesses`);

    } catch (error) {
      console.error('Connection test error:', error);
      toast.error('Connection test failed');
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Test Seed Page</h1>
      <p className="mb-4">This page is for testing database connection and seeding.</p>

      <div className="space-y-4">
        <Button
          onClick={testConnection}
          disabled={isTesting}
          variant="outline"
          className="mr-4"
        >
          {isTesting ? 'Testing...' : 'Test Database Connection'}
        </Button>

        <Button
          onClick={handleSeed}
          disabled={isSeeding}
          className="bg-[#007749] hover:bg-[#006739]"
        >
          {isSeeding ? 'Seeding...' : 'Seed Sample Businesses'}
        </Button>
      </div>
    </div>
  );
};

export default TestSeed;
