
import React, { useState, useRef, useEffect } from "react";
import { Send, Compass } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";

interface AIGuideChatProps {
  onClose: () => void;
}

type MessageType = {
  content: string;
  isUser: boolean;
  timestamp: Date;
};

const initialMessages = [
  {
    content: "Hello! I'm your South African guide. I can help you discover the best tours, restaurants, activities, and local experiences across South Africa. What are you interested in exploring?",
    isUser: false,
    timestamp: new Date(),
  },
];

// Sample responses for demo purposes
const sampleResponses: Record<string, string[]> = {
  default: [
    "I'd be happy to help you with that! Could you tell me which area of South Africa you're interested in exploring?",
    "That sounds like a wonderful idea! South Africa has so many options for that. Are you looking for something specific?",
    "Great question! There are several amazing options across South Africa. What kind of experience are you looking for?"
  ],
  tour: [
    "South Africa offers incredible safari tours at Kruger National Park where you can see the Big Five - lions, leopards, rhinos, elephants, and buffalos. Would you like more information about safari packages?",
    "Cape Town's Wine Route tours are fantastic for experiencing South Africa's renowned vineyards. You'll enjoy tastings at estates in Stellenbosch, Franschhoek, and Paarl. Would you like recommendations for specific wine estates?",
    "The Garden Route is one of South Africa's most scenic drives, stretching from Mossel Bay to Storms River. It features stunning coastlines, forests, and charming towns. I can suggest some guided tour options if you're interested."
  ],
  restaurant: [
    "For authentic South African cuisine, I recommend Moyo at Zoo Lake in Johannesburg. They serve traditional dishes like bobotie and potjiekos in a beautiful setting with live entertainment.",
    "The Test Kitchen in Cape Town is consistently ranked among the best restaurants in Africa. Chef Luke Dale-Roberts creates innovative dishes combining local ingredients with global techniques.",
    "If you're in Durban, try House of Curries on Florida Road for incredible bunny chow - a South African specialty of curry served in a hollowed-out loaf of bread that originated in the Indian community."
  ],
  activity: [
    "Shark cage diving in Gansbaai (near Cape Town) offers an exhilarating opportunity to see great white sharks up close in a safe environment. The best season is April to October.",
    "Hiking Table Mountain provides stunning panoramic views of Cape Town. There are multiple routes ranging from beginner to advanced, and you can take the cable car down if you prefer.",
    "The Apartheid Museum in Johannesburg offers a powerful and moving experience to understand South Africa's history. It's an important cultural visit that provides context to modern South Africa."
  ]
};

const AIGuideChat = ({ onClose }: AIGuideChatProps) => {
  const [messages, setMessages] = useState<MessageType[]>(initialMessages);
  const [input, setInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const endOfMessagesRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim()) return;
    
    // Add user message
    const userMessage = {
      content: input,
      isUser: true,
      timestamp: new Date(),
    };
    
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);
    
    // Simulate AI thinking
    setTimeout(() => {
      const aiResponse = generateResponse(input);
      setMessages((prev) => [...prev, {
        content: aiResponse,
        isUser: false,
        timestamp: new Date(),
      }]);
      setIsTyping(false);
    }, 1500);
  };

  const generateResponse = (query: string): string => {
    // Simple keyword matching for demo purposes
    const lowerQuery = query.toLowerCase();
    let responseArray: string[];
    
    if (lowerQuery.includes("tour") || lowerQuery.includes("safari") || lowerQuery.includes("sightseeing")) {
      responseArray = sampleResponses.tour;
    } else if (lowerQuery.includes("restaurant") || lowerQuery.includes("food") || lowerQuery.includes("eat")) {
      responseArray = sampleResponses.restaurant;
    } else if (lowerQuery.includes("activity") || lowerQuery.includes("things to do") || lowerQuery.includes("experience")) {
      responseArray = sampleResponses.activity;
    } else {
      responseArray = sampleResponses.default;
    }
    
    // Random response from appropriate category
    return responseArray[Math.floor(Math.random() * responseArray.length)];
  };

  return (
    <div className="bg-white rounded-lg shadow-xl w-[350px] md:w-[400px] h-[500px] flex flex-col">
      <div className="bg-[#003087] text-white p-4 rounded-t-lg flex items-center">
        <Compass className="mr-2 h-5 w-5" />
        <h3 className="font-semibold">South Africa Guide</h3>
      </div>
      
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.isUser ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  msg.isUser
                    ? "bg-[#007749] text-white"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                <p className="text-sm">{msg.content}</p>
                <p className="text-xs mt-1 opacity-70">
                  {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-gray-100">
                <div className="flex space-x-2">
                  <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"></div>
                  <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                  <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: "0.4s" }}></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={endOfMessagesRef} />
        </div>
      </ScrollArea>
      
      <form onSubmit={handleSendMessage} className="p-4 border-t flex gap-2">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask me about South Africa..."
          className="flex-1"
        />
        <Button type="submit" className="bg-[#007749] hover:bg-[#006739]">
          <Send className="h-4 w-4" />
        </Button>
      </form>
    </div>
  );
};

export default AIGuideChat;
