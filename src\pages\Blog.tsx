
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Layout from "@/components/layout/Layout";
import SEOHead from "@/components/seo/SEOHead";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Calendar, User, ArrowRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

type BlogCategory = {
  id: string;
  name: string;
  count: number;
  description: string;
};

const blogCategories: BlogCategory[] = [
  { id: "all", name: "All Posts", count: 0, description: "Browse all our latest articles" },
  { id: "travel-tips", name: "Travel Tips", count: 0, description: "Essential tips for traveling in South Africa" },
  { id: "destinations", name: "Best Destinations", count: 0, description: "Discover amazing places to visit" },
  { id: "business-travel", name: "Business Travel", count: 0, description: "Professional travel advice and tips" },
  { id: "local-guides", name: "Local Guides", count: 0, description: "Insider knowledge from locals" },
  { id: "events", name: "Events & Festivals", count: 0, description: "Upcoming events and celebrations" },
];

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  const { data: blogPosts = [], isLoading } = useQuery({
    queryKey: ['blog-posts', selectedCategory, searchQuery],
    queryFn: async () => {
      let query = supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'published')
        .order('published_at', { ascending: false });

      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,excerpt.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data || [];
    },
  });

  const filteredPosts = blogPosts;
  const featuredPost = filteredPosts[0];

  // Update category counts based on actual data
  const updatedCategories = blogCategories.map(category => ({
    ...category,
    count: category.id === "all" ? filteredPosts.length : filteredPosts.length
  }));

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "SA360 Travel & Business Blog",
    "description": "Expert travel tips, destination guides, and business insights for South Africa",
    "url": "https://sa360.co.za/blog",
    "publisher": {
      "@type": "Organization",
      "name": "SA360",
      "logo": {
        "@type": "ImageObject",
        "url": "https://sa360.co.za/lovable-uploads/b1ef264a-**************-253c678eb9d9.png"
      }
    },
    "blogPost": filteredPosts.slice(0, 5).map(post => ({
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.excerpt,
      "image": post.featured_image_url,
      "author": {
        "@type": "Person",
        "name": post.author_name
      },
      "datePublished": post.published_at,
      "publisher": {
        "@type": "Organization",
        "name": "SA360"
      }
    }))
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="py-12 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="animate-pulse space-y-8">
              <div className="text-center space-y-4">
                <div className="h-12 bg-gray-200 rounded mx-auto w-1/2"></div>
                <div className="h-6 bg-gray-200 rounded mx-auto w-3/4"></div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-6 space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-6 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <SEOHead
        title="South Africa Travel & Business Blog - Expert Tips & Guides"
        description="Get expert travel tips, destination guides, and business insights for South Africa. Discover the best places to visit, where to stay, what to do, and how to navigate business travel across the Rainbow Nation."
        keywords="South Africa travel blog, SA travel tips, business travel guide, Cape Town guide, Johannesburg travel, Durban attractions, Safari tips, Garden Route, travel advice"
        structuredData={structuredData}
      />
      <Layout>
        <div className="py-12 bg-slate-50">
          <div className="container mx-auto px-4">
            {/* SEO-optimized header section */}
            <header className="text-center mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
                South Africa Travel & Business Blog
              </h1>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-8">
                Discover the best destinations, travel tips, and business insights for exploring South Africa. 
                From safari adventures to city guides, we've got everything you need for your perfect trip.
              </p>
              
              {/* Search functionality */}
              <div className="max-w-md mx-auto relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input 
                  placeholder="Search articles..." 
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </header>

            {/* Category filters */}
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-800 mb-4">Browse by Category</h2>
              <div className="flex flex-wrap gap-3">
                {updatedCategories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`${
                      selectedCategory === category.id 
                        ? "bg-[#003087] hover:bg-[#00246a]" 
                        : "hover:bg-slate-100"
                    }`}
                  >
                    {category.name} ({category.count})
                  </Button>
                ))}
              </div>
            </section>
            
            {/* Featured article */}
            {featuredPost && (
              <article className="bg-white rounded-xl shadow-md overflow-hidden mb-12">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <img 
                      src={featuredPost.featured_image_url || "https://images.unsplash.com/photo-1484318571209-661cf29a69ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80"}
                      alt={featuredPost.title}
                      className="h-full w-full object-cover"
                      loading="eager"
                    />
                  </div>
                  <div className="md:w-1/2 p-6 md:p-8">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="secondary" className="bg-[#007749] text-white">
                        Featured
                      </Badge>
                    </div>
                    <h2 className="text-2xl md:text-3xl font-semibold text-slate-800 mb-3">
                      {featuredPost.title}
                    </h2>
                    <p className="text-slate-600 mb-4">
                      {featuredPost.excerpt}
                    </p>
                    <div className="flex items-center text-sm text-slate-500 mb-4">
                      <Calendar className="h-4 w-4 mr-1" />
                      <time dateTime={featuredPost.published_at || featuredPost.created_at}>
                        {featuredPost.published_at ? new Date(featuredPost.published_at).toLocaleDateString() : new Date(featuredPost.created_at).toLocaleDateString()}
                      </time>
                      <span className="mx-2">•</span>
                      <User className="h-4 w-4 mr-1" />
                      <span className="mr-4">{featuredPost.author_name || "SA360 Team"}</span>
                    </div>
                    <Link to={`/blog/${featuredPost.slug}`}>
                      <Button className="bg-[#003087] hover:bg-[#00246a]">
                        Read Full Article <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </article>
            )}
            
            {/* Blog post grid */}
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-800 mb-6">
                {selectedCategory === "all" ? "Latest Articles" : `${selectedCategory} Articles`}
              </h2>
              
              {filteredPosts.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-slate-600 text-lg mb-4">No articles found.</p>
                  <p className="text-slate-500">Check back soon for new content!</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredPosts.slice(1).map((post) => (
                    <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                      <img 
                        src={post.featured_image_url || "https://images.unsplash.com/photo-1484318571209-661cf29a69ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80"} 
                        alt={post.title} 
                        className="w-full h-48 object-cover"
                        loading="lazy"
                      />
                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-slate-800 mb-3 line-clamp-2">
                          {post.title}
                        </h3>
                        <p className="text-sm text-slate-600 mb-4 line-clamp-3">
                          {post.excerpt}
                        </p>
                        <div className="flex items-center text-xs text-slate-500 mb-4">
                          <Calendar className="h-3 w-3 mr-1" />
                          <time dateTime={post.published_at || post.created_at}>
                            {post.published_at ? new Date(post.published_at).toLocaleDateString() : new Date(post.created_at).toLocaleDateString()}
                          </time>
                          <span className="mx-2">•</span>
                          <User className="h-3 w-3 mr-1" />
                          <span>{post.author_name || "SA360 Team"}</span>
                        </div>
                        <Link to={`/blog/${post.slug}`}>
                          <Button variant="outline" className="w-full">
                            Read More
                          </Button>
                        </Link>
                      </div>
                    </article>
                  ))}
                </div>
              )}
            </section>
            
            {/* Newsletter subscription */}
            <section className="bg-[#003087] text-white rounded-xl p-8 md:p-10">
              <div className="max-w-3xl mx-auto text-center">
                <h3 className="text-2xl md:text-3xl font-bold mb-4">
                  Never Miss a Travel Update
                </h3>
                <p className="text-lg mb-6 opacity-90">
                  Get the latest travel tips, destination guides, and exclusive offers delivered to your inbox. 
                  Join thousands of travelers exploring South Africa with confidence.
                </p>
                <div className="flex flex-col md:flex-row gap-4 max-w-md mx-auto">
                  <Input 
                    placeholder="Enter your email address" 
                    className="bg-white text-black flex-1"
                    type="email"
                  />
                  <Button className="bg-[#FDB913] hover:bg-[#e9aa12] text-black font-semibold px-8">
                    Subscribe Now
                  </Button>
                </div>
                <p className="text-sm mt-3 opacity-75">
                  Free to subscribe. Unsubscribe anytime. No spam, ever.
                </p>
              </div>
            </section>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default Blog;
