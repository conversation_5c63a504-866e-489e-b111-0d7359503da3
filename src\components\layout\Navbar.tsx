
import React, { useState } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, Search, MessageSquareHeart, Gift, User, LogOut } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import LanguageSelector from "@/components/language/LanguageSelector";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const isMobile = useIsMobile();
  const { user, signOut } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  return (
    <header className="bg-white border-b border-slate-200 sticky top-0 z-30">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <img
              src="/lovable-uploads/b1ef264a-7970-4845-9757-253c678eb9d9.png"
              alt="South Africa Logo"
              style={{ height: "100px" }}
            />
            <span className="ml-2 text-xl font-bold text-slate-800">SA360</span>
          </Link>

          {/* Desktop navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            <NavLink
              to="/categories"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
            >
              Categories
            </NavLink>
            <NavLink
              to="/businesses"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
            >
              Businesses
            </NavLink>
            <NavLink
              to="/business-match"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                } flex items-center`
              }
            >
              <Search className="h-4 w-4 mr-1" />
              Business Match
            </NavLink>
            <NavLink
              to="/stories"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                } flex items-center`
              }
            >
              <MessageSquareHeart className="h-4 w-4 mr-1" />
              Stories
            </NavLink>
            <NavLink
              to="/special-offers"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                } flex items-center`
              }
            >
              <Gift className="h-4 w-4 mr-1" />
              Offers
            </NavLink>
            <NavLink
              to="/pricing"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
            >
              Pricing
            </NavLink>
            <NavLink
              to="/blog"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
            >
              Blog
            </NavLink>
          </nav>

          {/* Action buttons */}
          <div className="hidden md:flex items-center space-x-3">
            <LanguageSelector variant="compact" />
            {user ? (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span className="max-w-[150px] truncate">
                        {user.user_metadata?.full_name || user.email}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleSignOut}>
                      <LogOut className="h-4 w-4 mr-2" />
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button className="bg-[#007749] hover:bg-[#006739]" asChild>
                  <Link to="/submit-listing">+ Add Listing</Link>
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" asChild>
                  <Link to="/auth">Sign In</Link>
                </Button>
                <Button className="bg-[#007749] hover:bg-[#006739]" asChild>
                  <Link to="/auth">+ Add Listing</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden flex items-center"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6 text-slate-600" />
            ) : (
              <Menu className="h-6 w-6 text-slate-600" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobile && isMenuOpen && (
        <div className="md:hidden bg-white border-t border-slate-200 py-2">
          <div className="container mx-auto px-4 space-y-1">
            <NavLink
              to="/categories"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
              onClick={closeMenu}
            >
              Categories
            </NavLink>
            <NavLink
              to="/businesses"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
              onClick={closeMenu}
            >
              Businesses
            </NavLink>
            <NavLink
              to="/business-match"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                } flex items-center`
              }
              onClick={closeMenu}
            >
              <Search className="h-4 w-4 mr-2" />
              Business Match
            </NavLink>
            <NavLink
              to="/stories"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                } flex items-center`
              }
              onClick={closeMenu}
            >
              <MessageSquareHeart className="h-4 w-4 mr-2" />
              Business Stories
            </NavLink>
            <NavLink
              to="/special-offers"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                } flex items-center`
              }
              onClick={closeMenu}
            >
              <Gift className="h-4 w-4 mr-2" />
              Special Offers
            </NavLink>
            <NavLink
              to="/pricing"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
              onClick={closeMenu}
            >
              Pricing
            </NavLink>
            <NavLink
              to="/blog"
              className={({ isActive }) =>
                `block px-3 py-2 rounded-md text-base font-medium ${
                  isActive
                    ? "text-[#007749]"
                    : "text-slate-600 hover:text-[#007749]"
                }`
              }
              onClick={closeMenu}
            >
              Blog
            </NavLink>
            <div className="pt-2 pb-1">
              {user ? (
                <>
                  <div className="px-3 py-2 text-sm text-slate-600">
                    Signed in as: {user.user_metadata?.full_name || user.email}
                  </div>
                  <Button
                    variant="outline"
                    className="w-full mb-2 justify-center"
                    onClick={handleSignOut}
                  >
                    Sign Out
                  </Button>
                  <Button
                    className="w-full justify-center bg-[#007749] hover:bg-[#006739]"
                    asChild
                  >
                    <Link to="/submit-listing" onClick={closeMenu}>
                      + Add Listing
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outline"
                    className="w-full mb-2 justify-center"
                    asChild
                  >
                    <Link to="/auth" onClick={closeMenu}>
                      Sign In
                    </Link>
                  </Button>
                  <Button
                    className="w-full justify-center bg-[#007749] hover:bg-[#006739]"
                    asChild
                  >
                    <Link to="/auth" onClick={closeMenu}>
                      + Add Listing
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
