import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Plus, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  Calendar,
  DollarSign,
  Target,
  BarChart3,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

interface Campaign {
  id: string;
  name: string;
  description: string | null;
  budget_total: number;
  budget_spent: number;
  budget_daily: number | null;
  start_date: string;
  end_date: string | null;
  status: string;
  target_audience: any;
  keywords: string[] | null;
  created_at: string;
}

const CampaignManager = () => {
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    budget_total: '',
    budget_daily: '',
    start_date: '',
    end_date: '',
    target_audience: {
      age_range: '',
      location: '',
      interests: '',
      device_types: ''
    },
    keywords: ''
  });

  // Get user's business listings
  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id);
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  // Get campaigns
  const { data: campaigns, isLoading, refetch } = useQuery({
    queryKey: ['ad-campaigns', user?.id],
    queryFn: async () => {
      if (!user || !userBusinesses || userBusinesses.length === 0) return [];

      const businessIds = userBusinesses.map(b => b.id);
      const { data, error } = await supabase
        .from('ad_campaigns')
        .select('*')
        .in('business_id', businessIds)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Campaign[];
    },
    enabled: !!user && !!userBusinesses && userBusinesses.length > 0,
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      budget_total: '',
      budget_daily: '',
      start_date: '',
      end_date: '',
      target_audience: {
        age_range: '',
        location: '',
        interests: '',
        device_types: ''
      },
      keywords: ''
    });
    setEditingCampaign(null);
    setShowCreateForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !userBusinesses || userBusinesses.length === 0) return;

    setIsSubmitting(true);

    try {
      const campaignData = {
        business_id: userBusinesses[0].id, // Use first business for now
        name: formData.name,
        description: formData.description || null,
        budget_total: parseInt(formData.budget_total) * 100, // Convert to cents
        budget_daily: formData.budget_daily ? parseInt(formData.budget_daily) * 100 : null,
        start_date: formData.start_date,
        end_date: formData.end_date || null,
        target_audience: {
          age_range: formData.target_audience.age_range,
          location: formData.target_audience.location,
          interests: formData.target_audience.interests.split(',').map(i => i.trim()).filter(Boolean),
          device_types: formData.target_audience.device_types.split(',').map(d => d.trim()).filter(Boolean)
        },
        keywords: formData.keywords.split(',').map(k => k.trim()).filter(Boolean),
        status: 'draft'
      };

      if (editingCampaign) {
        const { error } = await supabase
          .from('ad_campaigns')
          .update(campaignData)
          .eq('id', editingCampaign.id);

        if (error) throw error;
        toast.success('Campaign updated successfully!');
      } else {
        const { error } = await supabase
          .from('ad_campaigns')
          .insert(campaignData);

        if (error) throw error;
        toast.success('Campaign created successfully!');
      }

      resetForm();
      refetch();
    } catch (error) {
      console.error('Error saving campaign:', error);
      toast.error('Failed to save campaign');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateCampaignStatus = async (campaignId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('ad_campaigns')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', campaignId);

      if (error) throw error;

      toast.success(`Campaign ${status === 'active' ? 'activated' : 'paused'} successfully!`);
      refetch();
    } catch (error) {
      console.error('Error updating campaign status:', error);
      toast.error('Failed to update campaign status');
    }
  };

  const deleteCampaign = async (campaignId: string) => {
    if (!confirm('Are you sure you want to delete this campaign?')) return;

    try {
      const { error } = await supabase
        .from('ad_campaigns')
        .delete()
        .eq('id', campaignId);

      if (error) throw error;

      toast.success('Campaign deleted successfully!');
      refetch();
    } catch (error) {
      console.error('Error deleting campaign:', error);
      toast.error('Failed to delete campaign');
    }
  };

  const editCampaign = (campaign: Campaign) => {
    setFormData({
      name: campaign.name,
      description: campaign.description || '',
      budget_total: (campaign.budget_total / 100).toString(),
      budget_daily: campaign.budget_daily ? (campaign.budget_daily / 100).toString() : '',
      start_date: campaign.start_date.split('T')[0],
      end_date: campaign.end_date ? campaign.end_date.split('T')[0] : '',
      target_audience: {
        age_range: campaign.target_audience?.age_range || '',
        location: campaign.target_audience?.location || '',
        interests: campaign.target_audience?.interests?.join(', ') || '',
        device_types: campaign.target_audience?.device_types?.join(', ') || ''
      },
      keywords: campaign.keywords?.join(', ') || ''
    });
    setEditingCampaign(campaign);
    setShowCreateForm(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to manage your ad campaigns</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-800">Campaign Manager</h2>
          <p className="text-slate-600">Create and manage your advertising campaigns</p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-[#007749] hover:bg-[#006739]"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Campaign
        </Button>
      </div>

      {/* Create/Edit Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingCampaign ? 'Edit Campaign' : 'Create New Campaign'}
            </CardTitle>
            <CardDescription>
              Set up your advertising campaign with targeting and budget settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Campaign Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    placeholder="Summer Sale Campaign"
                  />
                </div>

                <div>
                  <Label htmlFor="budget_total">Total Budget (R) *</Label>
                  <Input
                    id="budget_total"
                    type="number"
                    value={formData.budget_total}
                    onChange={(e) => setFormData(prev => ({ ...prev, budget_total: e.target.value }))}
                    required
                    placeholder="1000"
                    min="1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your campaign goals and target audience..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="budget_daily">Daily Budget (R)</Label>
                  <Input
                    id="budget_daily"
                    type="number"
                    value={formData.budget_daily}
                    onChange={(e) => setFormData(prev => ({ ...prev, budget_daily: e.target.value }))}
                    placeholder="50"
                    min="1"
                  />
                </div>

                <div>
                  <Label htmlFor="start_date">Start Date *</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                    required
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <Label htmlFor="end_date">End Date</Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                    min={formData.start_date}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Targeting Options</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={formData.target_audience.location}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        target_audience: { ...prev.target_audience, location: e.target.value }
                      }))}
                      placeholder="Cape Town, Johannesburg"
                    />
                  </div>

                  <div>
                    <Label htmlFor="age_range">Age Range</Label>
                    <Select 
                      value={formData.target_audience.age_range}
                      onValueChange={(value) => setFormData(prev => ({ 
                        ...prev, 
                        target_audience: { ...prev.target_audience, age_range: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select age range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="18-24">18-24</SelectItem>
                        <SelectItem value="25-34">25-34</SelectItem>
                        <SelectItem value="35-44">35-44</SelectItem>
                        <SelectItem value="45-54">45-54</SelectItem>
                        <SelectItem value="55+">55+</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="interests">Interests (comma-separated)</Label>
                  <Input
                    id="interests"
                    value={formData.target_audience.interests}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      target_audience: { ...prev.target_audience, interests: e.target.value }
                    }))}
                    placeholder="food, dining, restaurants, entertainment"
                  />
                </div>

                <div>
                  <Label htmlFor="keywords">Keywords (comma-separated)</Label>
                  <Input
                    id="keywords"
                    value={formData.keywords}
                    onChange={(e) => setFormData(prev => ({ ...prev, keywords: e.target.value }))}
                    placeholder="restaurant, food delivery, cape town dining"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-[#007749] hover:bg-[#006739]"
                >
                  {isSubmitting ? 'Saving...' : editingCampaign ? 'Update Campaign' : 'Create Campaign'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Campaigns List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Campaigns</CardTitle>
          <CardDescription>
            Manage your active and draft advertising campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#007749] mx-auto mb-4"></div>
              <p className="text-slate-600">Loading campaigns...</p>
            </div>
          ) : !campaigns || campaigns.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600 mb-2">No campaigns yet</p>
              <p className="text-sm text-slate-500">Create your first campaign to start advertising</p>
            </div>
          ) : (
            <div className="space-y-4">
              {campaigns.map((campaign) => (
                <Card key={campaign.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{campaign.name}</h3>
                          <Badge className={getStatusColor(campaign.status)}>
                            {campaign.status}
                          </Badge>
                        </div>
                        
                        {campaign.description && (
                          <p className="text-slate-600 mb-3">{campaign.description}</p>
                        )}
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-slate-500">Budget</p>
                            <p className="font-semibold">R{(campaign.budget_total / 100).toFixed(2)}</p>
                          </div>
                          <div>
                            <p className="text-slate-500">Spent</p>
                            <p className="font-semibold">R{(campaign.budget_spent / 100).toFixed(2)}</p>
                          </div>
                          <div>
                            <p className="text-slate-500">Start Date</p>
                            <p className="font-semibold">{new Date(campaign.start_date).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <p className="text-slate-500">End Date</p>
                            <p className="font-semibold">
                              {campaign.end_date ? new Date(campaign.end_date).toLocaleDateString() : 'Ongoing'}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {campaign.status === 'draft' || campaign.status === 'paused' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateCampaignStatus(campaign.id, 'active')}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateCampaignStatus(campaign.id, 'paused')}
                          >
                            <Pause className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => editCampaign(campaign)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deleteCampaign(campaign.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignManager;
