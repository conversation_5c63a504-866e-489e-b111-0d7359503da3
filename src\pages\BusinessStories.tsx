
import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Heart, MessageSquareHeart, Share2 } from "lucide-react";
import { Link } from "react-router-dom";

type Story = {
  id: string;
  businessName: string;
  title: string;
  excerpt: string;
  thumbnail: string;
  category: string;
  isPremium: boolean;
  date: string;
  type: "video" | "article" | "podcast";
  views: number;
  likes: number;
};

const mockStories: Story[] = [
  {
    id: "1",
    businessName: "Shuter & Shooter Publishers",
    title: "From Small Town to National Icon: The Story of a South African Publishing House",
    excerpt: "How Shuter & Shooter preserved South African literature through decades of change.",
    thumbnail: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80",
    category: "Education",
    isPremium: true,
    date: "May 10, 2025",
    type: "video",
    views: 3452,
    likes: 289
  },
  {
    id: "2",
    businessName: "Ukhamba Beerworx",
    title: "Brewing Success: Township Craft Beer Revolution",
    excerpt: "How two friends turned their passion for beer into Cape Town's favorite craft brewery.",
    thumbnail: "https://images.unsplash.com/photo-1584225064785-c62a8b43d148?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80",
    category: "Food & Beverage",
    isPremium: false,
    date: "May 5, 2025",
    type: "article",
    views: 2854,
    likes: 347
  },
  {
    id: "3",
    businessName: "Africology",
    title: "Natural Beauty: From Kitchen to Global Luxury Spa Brand",
    excerpt: "The journey of South Africa's leading natural skincare brand and its sustainable practices.",
    thumbnail: "https://images.unsplash.com/photo-1612817288484-6f916006741a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80",
    category: "Beauty & Wellness",
    isPremium: true,
    date: "April 28, 2025",
    type: "video",
    views: 7821,
    likes: 943
  },
  {
    id: "4",
    businessName: "Bathu Shoes",
    title: "Walking the Path of Success: South Africa's Sneaker Revolution",
    excerpt: "How a township entrepreneur built a national footwear brand that celebrates African heritage.",
    thumbnail: "https://images.unsplash.com/photo-1604671801908-6f0c6a092c05?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    category: "Retail",
    isPremium: false,
    date: "April 20, 2025",
    type: "podcast",
    views: 5467,
    likes: 721
  },
  {
    id: "5",
    businessName: "Somo Coal",
    title: "Powering Communities: The Family Mining Business Making a Difference",
    excerpt: "How a female-led mining company is changing lives in rural South Africa.",
    thumbnail: "https://images.unsplash.com/photo-1581094288338-2314dddb7ece?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    category: "Mining & Resources",
    isPremium: true,
    date: "April 15, 2025",
    type: "video",
    views: 3189,
    likes: 412
  },
  {
    id: "6",
    businessName: "RLabs",
    title: "Tech for Good: The Social Enterprise Transforming Lives",
    excerpt: "From gangster to tech entrepreneur: The inspiring story behind Cape Town's digital innovation hub.",
    thumbnail: "https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    category: "Technology",
    isPremium: false,
    date: "April 8, 2025",
    type: "article",
    views: 4235,
    likes: 567
  }
];

const BusinessStories = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [likedStories, setLikedStories] = useState<Record<string, boolean>>({});

  const handleLike = (storyId: string) => {
    setLikedStories(prev => ({
      ...prev,
      [storyId]: !prev[storyId]
    }));
  };

  const filteredStories = activeTab === "all" 
    ? mockStories 
    : mockStories.filter(story => story.type === activeTab);

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
              South African Business Stories
            </h1>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Discover the inspiring journeys, challenges, and triumphs behind 
              South Africa's most notable businesses.
            </p>
          </div>

          <Tabs defaultValue="all" className="mb-8" onValueChange={setActiveTab}>
            <div className="flex justify-center mb-6">
              <TabsList>
                <TabsTrigger value="all">All Stories</TabsTrigger>
                <TabsTrigger value="video">Videos</TabsTrigger>
                <TabsTrigger value="article">Articles</TabsTrigger>
                <TabsTrigger value="podcast">Podcasts</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="video" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="article" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="podcast" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredStories.map((story) => (
                  <StoryCard 
                    key={story.id} 
                    story={story}
                    isLiked={!!likedStories[story.id]}
                    onLike={handleLike}
                  />
                ))}
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="text-center mt-10">
            <Button className="bg-[#007749] hover:bg-[#006739]">
              Load More Stories
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

interface StoryCardProps {
  story: Story;
  isLiked: boolean;
  onLike: (storyId: string) => void;
}

const StoryCard = ({ story, isLiked, onLike }: StoryCardProps) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <div className="absolute top-2 right-2 bg-red-600 p-1 rounded-full"><MessageSquareHeart className="h-4 w-4 text-white" /></div>;
      case "podcast":
        return <div className="absolute top-2 right-2 bg-purple-600 p-1 rounded-full"><MessageSquareHeart className="h-4 w-4 text-white" /></div>;
      default:
        return <div className="absolute top-2 right-2 bg-blue-600 p-1 rounded-full"><MessageSquareHeart className="h-4 w-4 text-white" /></div>;
    }
  };

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="relative">
        <Link to={`/stories/${story.id}`}>
          <div className="aspect-[16/9] overflow-hidden">
            <img 
              src={story.thumbnail} 
              alt={story.title} 
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        </Link>
        {getTypeIcon(story.type)}
        {story.isPremium && (
          <Badge className="absolute top-2 left-2 bg-[#FDB913] text-black">Premium</Badge>
        )}
      </div>
      
      <CardContent className="flex-1 flex flex-col p-5">
        <div className="mb-2">
          <span className="text-sm text-[#007749] font-medium">{story.category}</span>
        </div>
        
        <Link to={`/stories/${story.id}`} className="group">
          <h3 className="font-semibold text-lg text-slate-800 mb-2 group-hover:text-[#007749] transition-colors">
            {story.title}
          </h3>
        </Link>
        
        <p className="text-slate-600 text-sm mb-4 flex-1">{story.excerpt}</p>
        
        <div className="mt-auto">
          <div className="flex justify-between items-center">
            <span className="text-sm text-slate-500">{story.date}</span>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => onLike(story.id)}
                className={`flex items-center text-sm ${isLiked ? 'text-red-500' : 'text-slate-500'}`}
              >
                <Heart className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                {story.likes + (isLiked ? 1 : 0)}
              </button>
              <button className="flex items-center text-sm text-slate-500">
                <Share2 className="h-4 w-4 mr-1" />
                Share
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BusinessStories;
