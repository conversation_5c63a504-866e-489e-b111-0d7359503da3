
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[PAYPAL-SUBSCRIPTION] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError) throw new Error(`Authentication error: ${userError.message}`);
    const user = userData.user;
    if (!user?.email) throw new Error("User not authenticated or email not available");

    const { tier, businessId } = await req.json();
    logStep("Request data", { tier, businessId, userId: user.id });

    // PayPal pricing configuration (in USD)
    const pricingConfig = {
      'silver': { amount: '29.99', name: 'Silver Premium' },
      'gold': { amount: '54.99', name: 'Gold Premium' },
      'platinum': { amount: '89.99', name: 'Platinum Premium' }
    };

    const selectedPlan = pricingConfig[tier as keyof typeof pricingConfig];
    if (!selectedPlan) throw new Error("Invalid subscription tier");

    const clientId = Deno.env.get("PAYPAL_CLIENT_ID");
    const clientSecret = Deno.env.get("PAYPAL_CLIENT_SECRET");
    
    if (!clientId || !clientSecret) {
      throw new Error("PayPal credentials not configured");
    }

    // Get PayPal access token
    const tokenResponse = await fetch('https://api.sandbox.paypal.com/v1/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(`${clientId}:${clientSecret}`)}`
      },
      body: 'grant_type=client_credentials'
    });

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    const origin = req.headers.get("origin") || "http://localhost:3000";
    const orderId = `paypal_sub_${Date.now()}_${user.id.slice(0, 8)}`;

    // Create PayPal subscription
    const subscriptionData = {
      plan_id: `${tier}_plan`, // This would be pre-created in PayPal
      quantity: "1",
      subscriber: {
        name: {
          given_name: user.user_metadata?.full_name?.split(' ')[0] || "User",
          surname: user.user_metadata?.full_name?.split(' ').slice(1).join(' ') || "Name"
        },
        email_address: user.email
      },
      application_context: {
        brand_name: "SA360 Business Directory",
        locale: "en-US",
        shipping_preference: "NO_SHIPPING",
        user_action: "SUBSCRIBE_NOW",
        payment_method: {
          payer_selected: "PAYPAL",
          payee_preferred: "IMMEDIATE_PAYMENT_REQUIRED"
        },
        return_url: `${origin}/subscription-success`,
        cancel_url: `${origin}/pricing`
      },
      custom_id: orderId
    };

    const subscriptionResponse = await fetch('https://api.sandbox.paypal.com/v1/billing/subscriptions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'PayPal-Request-Id': orderId
      },
      body: JSON.stringify(subscriptionData)
    });

    const subscription = await subscriptionResponse.json();
    
    if (!subscriptionResponse.ok) {
      logStep("PayPal subscription creation failed", subscription);
      throw new Error("Failed to create PayPal subscription");
    }

    // Store pending subscription
    const { error: insertError } = await supabaseClient.from("subscriptions").insert({
      user_id: user.id,
      business_id: businessId,
      tier,
      status: "pending",
      amount: Math.round(parseFloat(selectedPlan.amount) * 100), // Convert to cents
      currency: "USD",
      stripe_subscription_id: subscription.id,
    });

    if (insertError) {
      logStep("Error storing subscription", insertError);
      throw new Error("Failed to store subscription");
    }

    logStep("PayPal subscription created successfully", { subscriptionId: subscription.id });

    // Find approval URL
    const approvalLink = subscription.links.find((link: any) => link.rel === "approve");
    
    return new Response(JSON.stringify({ 
      subscriptionId: subscription.id,
      approvalUrl: approvalLink?.href 
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
