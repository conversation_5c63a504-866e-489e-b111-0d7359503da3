
-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table for additional user information
CREATE TABLE public.profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Create categories table
CREATE TABLE public.categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  icon TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create business listings table
CREATE TABLE public.business_listings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  category_id UUID REFERENCES public.categories(id),
  description TEXT,
  full_address TEXT,
  city TEXT,
  phone TEXT,
  website TEXT,
  price_range TEXT,
  price_from DECIMAL,
  price_to DECIMAL,
  tags TEXT[],
  business_hours JSONB,
  social_media_links JSONB,
  images TEXT[],
  logo_url TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create FAQs table for business listings
CREATE TABLE public.business_faqs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE NOT NULL,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default categories
INSERT INTO public.categories (name, slug, description) VALUES
('Accommodation', 'accommodation', 'Hotels, B&Bs, and lodging options'),
('Arts & Entertainment', 'arts-entertainment', 'Theaters, galleries, and entertainment venues'),
('Automotive', 'automotive', 'Car services, dealerships, and repairs'),
('Backpackers', 'backpackers', 'Budget accommodation for travelers'),
('Barber', 'barber', 'Barber shops and grooming services'),
('Beauty & Spa', 'beauty-spa', 'Beauty salons and spa services'),
('Bed & Breakfast', 'bed-breakfast', 'Cozy B&B accommodations'),
('Businesses', 'businesses', 'General business services'),
('Café', 'cafe', 'Coffee shops and casual dining'),
('Camping Sites', 'camping', 'Outdoor camping and caravan parks'),
('Casino', 'casino', 'Gaming and entertainment venues'),
('Clinics', 'clinics', 'Medical and healthcare clinics'),
('Coffee Shop', 'coffee-shop', 'Specialty coffee and beverages'),
('Construction', 'construction', 'Building and construction services'),
('Farmers Market', 'farmers-market', 'Fresh produce and local goods'),
('Finance', 'finance', 'Financial services and banking'),
('Flea Market', 'flea-market', 'Second-hand goods and antiques'),
('Food', 'food', 'Food services and dining'),
('Food Market', 'food-market', 'Grocery stores and food markets'),
('Game Drives', 'game-drives', 'Wildlife and safari experiences'),
('Health & Medical', 'health-medical', 'Healthcare and medical services'),
('Hotel', 'hotel', 'Hotel accommodations'),
('Real Estate', 'real-estate', 'Property sales and rentals'),
('Restaurant', 'restaurant', 'Dining and restaurant services'),
('Shopping', 'shopping', 'Retail stores and shopping centers');

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_faqs ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Business listings policies
CREATE POLICY "Anyone can view approved listings" ON public.business_listings
  FOR SELECT USING (status = 'approved' OR auth.uid() = user_id);

CREATE POLICY "Users can create their own listings" ON public.business_listings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own listings" ON public.business_listings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own listings" ON public.business_listings
  FOR DELETE USING (auth.uid() = user_id);

-- Business FAQs policies
CREATE POLICY "Anyone can view FAQs for approved listings" ON public.business_faqs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.business_listings 
      WHERE id = business_faqs.business_id 
      AND (status = 'approved' OR user_id = auth.uid())
    )
  );

CREATE POLICY "Users can manage FAQs for their own listings" ON public.business_faqs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.business_listings 
      WHERE id = business_faqs.business_id 
      AND user_id = auth.uid()
    )
  );

-- Categories are publicly readable
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Categories are publicly readable" ON public.categories
  FOR SELECT USING (true);

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_business_listings_updated_at
  BEFORE UPDATE ON public.business_listings
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
