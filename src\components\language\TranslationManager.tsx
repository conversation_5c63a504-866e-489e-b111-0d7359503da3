import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Languages, 
  Save, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Globe,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { languageNames, languageFlags } from '@/contexts/LanguageContext';

interface BusinessTranslation {
  id: string;
  business_id: string;
  language_code: string;
  title: string | null;
  description: string | null;
  translated_by: string;
  translation_quality: number;
  is_verified: boolean;
}

interface TranslationManagerProps {
  businessId: string;
  originalTitle: string;
  originalDescription: string;
}

const TranslationManager = ({ businessId, originalTitle, originalDescription }: TranslationManagerProps) => {
  const { user } = useAuth();
  const [activeLanguage, setActiveLanguage] = useState<'af' | 'zu'>('af');
  const [isGenerating, setIsGenerating] = useState(false);
  const [translations, setTranslations] = useState<Record<string, { title: string; description: string }>>({
    af: { title: '', description: '' },
    zu: { title: '', description: '' }
  });

  const { data: existingTranslations, refetch } = useQuery({
    queryKey: ['business-translations', businessId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('business_listing_translations')
        .select('*')
        .eq('business_id', businessId);

      if (error) throw error;
      return data as BusinessTranslation[];
    },
    enabled: !!businessId,
  });

  // Load existing translations into form
  useEffect(() => {
    if (existingTranslations) {
      const translationMap: Record<string, { title: string; description: string }> = {
        af: { title: '', description: '' },
        zu: { title: '', description: '' }
      };

      existingTranslations.forEach(translation => {
        if (translation.language_code === 'af' || translation.language_code === 'zu') {
          translationMap[translation.language_code] = {
            title: translation.title || '',
            description: translation.description || ''
          };
        }
      });

      setTranslations(translationMap);
    }
  }, [existingTranslations]);

  const generateAutoTranslation = async (targetLanguage: 'af' | 'zu') => {
    setIsGenerating(true);
    
    try {
      // This is a mock auto-translation. In a real app, you'd use a translation service
      // like Google Translate API, Azure Translator, or AWS Translate
      
      const mockTranslations = {
        af: {
          title: originalTitle + ' (Afrikaans)',
          description: originalDescription ? originalDescription + ' (Vertaal na Afrikaans)' : ''
        },
        zu: {
          title: originalTitle + ' (isiZulu)',
          description: originalDescription ? originalDescription + ' (Iguqulelwe ku-isiZulu)' : ''
        }
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      setTranslations(prev => ({
        ...prev,
        [targetLanguage]: mockTranslations[targetLanguage]
      }));

      toast.success(`Auto-translation generated for ${languageNames[targetLanguage]}!`, {
        description: 'Please review and edit the translation as needed.'
      });
    } catch (error) {
      console.error('Error generating translation:', error);
      toast.error('Failed to generate auto-translation');
    } finally {
      setIsGenerating(false);
    }
  };

  const saveTranslation = async (languageCode: 'af' | 'zu') => {
    if (!user) return;

    const translation = translations[languageCode];
    if (!translation.title.trim()) {
      toast.error('Title is required');
      return;
    }

    try {
      const { error } = await supabase
        .from('business_listing_translations')
        .upsert({
          business_id: businessId,
          language_code: languageCode,
          title: translation.title.trim(),
          description: translation.description.trim() || null,
          translated_by: 'business_owner',
          translation_quality: 1.0, // Human translation gets full quality score
          is_verified: true,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast.success(`${languageNames[languageCode]} translation saved successfully!`);
      refetch();
    } catch (error) {
      console.error('Error saving translation:', error);
      toast.error('Failed to save translation');
    }
  };

  const updateTranslation = (languageCode: 'af' | 'zu', field: 'title' | 'description', value: string) => {
    setTranslations(prev => ({
      ...prev,
      [languageCode]: {
        ...prev[languageCode],
        [field]: value
      }
    }));
  };

  const getTranslationStatus = (languageCode: 'af' | 'zu') => {
    const existing = existingTranslations?.find(t => t.language_code === languageCode);
    if (!existing) return { status: 'missing', color: 'bg-gray-100 text-gray-800' };
    
    if (existing.is_verified && existing.translated_by === 'business_owner') {
      return { status: 'verified', color: 'bg-green-100 text-green-800' };
    }
    
    if (existing.translated_by === 'auto') {
      return { status: 'auto', color: 'bg-yellow-100 text-yellow-800' };
    }
    
    return { status: 'draft', color: 'bg-blue-100 text-blue-800' };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Languages className="h-5 w-5 text-[#007749]" />
          Translation Management
        </CardTitle>
        <CardDescription>
          Manage translations of your business listing in Afrikaans and isiZulu to reach more customers
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeLanguage} onValueChange={(value) => setActiveLanguage(value as 'af' | 'zu')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="af" className="flex items-center gap-2">
              <span>{languageFlags.af}</span>
              Afrikaans
              <Badge className={getTranslationStatus('af').color} variant="secondary">
                {getTranslationStatus('af').status}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="zu" className="flex items-center gap-2">
              <span>{languageFlags.zu}</span>
              isiZulu
              <Badge className={getTranslationStatus('zu').color} variant="secondary">
                {getTranslationStatus('zu').status}
              </Badge>
            </TabsTrigger>
          </TabsList>

          {(['af', 'zu'] as const).map((lang) => (
            <TabsContent key={lang} value={lang} className="space-y-6">
              {/* Original Content Reference */}
              <Card className="bg-slate-50">
                <CardContent className="p-4">
                  <h4 className="font-semibold text-slate-700 mb-2 flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Original (English)
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Title:</span> {originalTitle}
                    </div>
                    {originalDescription && (
                      <div>
                        <span className="font-medium">Description:</span> {originalDescription}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Translation Form */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-slate-700">
                    {languageNames[lang]} Translation
                  </h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => generateAutoTranslation(lang)}
                    disabled={isGenerating}
                    className="flex items-center gap-2"
                  >
                    {isGenerating ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Zap className="h-4 w-4" />
                    )}
                    Auto-translate
                  </Button>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-700">
                    Business Title *
                  </label>
                  <Input
                    value={translations[lang].title}
                    onChange={(e) => updateTranslation(lang, 'title', e.target.value)}
                    placeholder={`Enter business title in ${languageNames[lang]}`}
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-700">
                    Business Description
                  </label>
                  <Textarea
                    value={translations[lang].description}
                    onChange={(e) => updateTranslation(lang, 'description', e.target.value)}
                    placeholder={`Enter business description in ${languageNames[lang]}`}
                    rows={4}
                    className="mt-1"
                  />
                </div>

                <div className="flex items-center gap-3">
                  <Button
                    onClick={() => saveTranslation(lang)}
                    disabled={!translations[lang].title.trim()}
                    className="bg-[#007749] hover:bg-[#006739]"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Translation
                  </Button>

                  {getTranslationStatus(lang).status === 'verified' && (
                    <div className="flex items-center gap-1 text-green-600 text-sm">
                      <CheckCircle className="h-4 w-4" />
                      Verified
                    </div>
                  )}

                  {getTranslationStatus(lang).status === 'auto' && (
                    <div className="flex items-center gap-1 text-yellow-600 text-sm">
                      <AlertCircle className="h-4 w-4" />
                      Auto-translated - Please review
                    </div>
                  )}
                </div>
              </div>

              {/* Translation Tips */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <h5 className="font-semibold text-blue-800 mb-2">Translation Tips</h5>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Keep your business name recognizable - you may choose not to translate it</li>
                    <li>• Focus on translating the description and key services</li>
                    <li>• Use natural, conversational language that locals would use</li>
                    <li>• Consider cultural context and local preferences</li>
                    {lang === 'af' && (
                      <li>• Afrikaans speakers appreciate authentic, warm communication</li>
                    )}
                    {lang === 'zu' && (
                      <li>• isiZulu speakers value respectful and community-focused messaging</li>
                    )}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default TranslationManager;
