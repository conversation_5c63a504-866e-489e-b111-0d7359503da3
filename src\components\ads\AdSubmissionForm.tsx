import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Upload, Eye, DollarSign, Target, Calendar, Settings, BarChart3 } from 'lucide-react';

const AdSubmissionForm = () => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState('campaign');
  const [campaignData, setCampaignData] = useState({
    name: '',
    description: '',
    budget_total: '',
    budget_daily: '',
    start_date: '',
    end_date: '',
    target_audience: {
      age_range: '',
      location: '',
      interests: [],
      device_types: []
    },
    keywords: []
  });
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    target_url: '',
    ad_type: 'banner',
    position: 'top',
    image_url: '',
    priority: 1,
    business_id: '',
    terms_accepted: false
  });

  const adTypes = [
    { value: 'banner', label: 'Banner Ad', description: 'Standard banner advertisement' },
    { value: 'sidebar', label: 'Sidebar Ad', description: 'Sidebar placement advertisement' },
    { value: 'sponsored_listing', label: 'Sponsored Listing', description: 'Promoted business listing' }
  ];

  const positions = [
    { value: 'top', label: 'Top of Page', price: 'R500/month' },
    { value: 'middle', label: 'Middle of Content', price: 'R300/month' },
    { value: 'bottom', label: 'Bottom of Page', price: 'R200/month' },
    { value: 'sidebar', label: 'Sidebar', price: 'R400/month' }
  ];

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size must be less than 2MB');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    try {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to Supabase Storage
      const fileExt = file.name.split('.').pop();
      const fileName = `ad-${Date.now()}.${fileExt}`;
      
      const { data, error } = await supabase.storage
        .from('ad-images')
        .upload(fileName, file);

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('ad-images')
        .getPublicUrl(fileName);

      setFormData(prev => ({ ...prev, image_url: publicUrl }));
      toast.success('Image uploaded successfully');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error('Please sign in to submit an ad');
      return;
    }

    if (!formData.terms_accepted) {
      toast.error('Please accept the terms and conditions');
      return;
    }

    setIsSubmitting(true);

    try {
      const adData = {
        title: formData.title,
        description: formData.description || null,
        image_url: formData.image_url || null,
        target_url: formData.target_url,
        ad_type: formData.ad_type,
        position: formData.position,
        budget_total: formData.budget_total ? parseInt(formData.budget_total) * 100 : null, // Convert to cents
        start_date: formData.start_date || new Date().toISOString(),
        end_date: formData.end_date || null,
        business_id: formData.business_id || null,
        status: 'pending'
      };

      const { error } = await supabase
        .from('ads')
        .insert(adData);

      if (error) throw error;

      toast.success('Ad submitted successfully! We will review it and get back to you within 24 hours.');
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        target_url: '',
        ad_type: 'banner',
        position: 'top',
        image_url: '',
        budget_total: '',
        start_date: '',
        end_date: '',
        business_id: '',
        terms_accepted: false
      });
      setImagePreview(null);
    } catch (error) {
      console.error('Error submitting ad:', error);
      toast.error('Failed to submit ad. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-[#007749]" />
          Submit Advertisement
        </CardTitle>
        <CardDescription>
          Promote your business with targeted advertising on our platform
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label htmlFor="title">Ad Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              required
              placeholder="Enter your ad title"
              maxLength={100}
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of your ad"
              rows={3}
              maxLength={200}
            />
          </div>

          <div>
            <Label htmlFor="target_url">Target URL *</Label>
            <Input
              id="target_url"
              type="url"
              value={formData.target_url}
              onChange={(e) => setFormData(prev => ({ ...prev, target_url: e.target.value }))}
              required
              placeholder="https://your-website.com"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="ad_type">Ad Type</Label>
              <Select value={formData.ad_type} onValueChange={(value) => setFormData(prev => ({ ...prev, ad_type: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {adTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-slate-500">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="position">Position</Label>
              <Select value={formData.position} onValueChange={(value) => setFormData(prev => ({ ...prev, position: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {positions.map((pos) => (
                    <SelectItem key={pos.value} value={pos.value}>
                      <div className="flex justify-between items-center w-full">
                        <span>{pos.label}</span>
                        <span className="text-[#007749] font-medium">{pos.price}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="image">Ad Image</Label>
            <div className="mt-2">
              <input
                type="file"
                id="image"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('image')?.click()}
                className="w-full"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Image (Max 2MB)
              </Button>
              {imagePreview && (
                <div className="mt-4">
                  <img
                    src={imagePreview}
                    alt="Ad preview"
                    className="max-w-full h-32 object-cover rounded border"
                  />
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start_date">Start Date</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div>
              <Label htmlFor="end_date">End Date (Optional)</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                min={formData.start_date || new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <Checkbox
              id="terms"
              checked={formData.terms_accepted}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, terms_accepted: checked as boolean }))}
            />
            <div className="grid gap-1.5 leading-none">
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                I accept the advertising terms and conditions
              </label>
              <p className="text-xs text-slate-500">
                Your ad will be reviewed within 24 hours. Payment will be processed upon approval.
              </p>
            </div>
          </div>

          <Button 
            type="submit" 
            className="w-full bg-[#007749] hover:bg-[#006739]"
            disabled={isSubmitting || !formData.title || !formData.target_url || !formData.terms_accepted}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Ad for Review'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default AdSubmissionForm;
