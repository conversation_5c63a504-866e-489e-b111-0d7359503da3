
-- Update business_listings table to ensure all premium features are supported
ALTER TABLE business_listings 
ADD COLUMN IF NOT EXISTS premium_placement_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_premium_update TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Create an index for efficient premium sorting
CREATE INDEX IF NOT EXISTS idx_business_listings_premium_sort 
ON business_listings (premium_placement_score DESC, featured DESC, created_at DESC);

-- Update subscription tiers to include placement scores
UPDATE business_listings 
SET premium_placement_score = 
  CASE subscription_tier
    WHEN 'platinum' THEN 100
    WHEN 'gold' THEN 80
    WHEN 'silver' THEN 60
    ELSE 0
  END;

-- Create a function to automatically update premium placement scores
CREATE OR REPLACE FUNCTION update_premium_placement()
RETURNS TRIGGER AS $$
BEGIN
  -- Update placement score based on subscription tier
  NEW.premium_placement_score = 
    CASE NEW.subscription_tier
      WHEN 'platinum' THEN 100
      WHEN 'gold' THEN 80
      WHEN 'silver' THEN 60
      ELSE 0
    END;
  
  -- Set featured flag for premium tiers
  NEW.featured = (NEW.subscription_tier IN ('gold', 'platinum'));
  
  -- Update last premium update timestamp
  NEW.last_premium_update = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update premium features
DROP TRIGGER IF EXISTS trigger_update_premium_placement ON business_listings;
CREATE TRIGGER trigger_update_premium_placement
  BEFORE INSERT OR UPDATE OF subscription_tier ON business_listings
  FOR EACH ROW
  EXECUTE FUNCTION update_premium_placement();

-- Add real-time support for business listings
ALTER TABLE business_listings REPLICA IDENTITY FULL;

-- Add business_listings to realtime publication
INSERT INTO supabase_realtime.subscription (id, subscription_id, filters)
SELECT 
  gen_random_uuid(),
  'realtime',
  ARRAY['{"event": "*", "schema": "public", "table": "business_listings"}']
ON CONFLICT DO NOTHING;
