import React from 'react';
import Layout from '@/components/layout/Layout';
import EventCalendar from '@/components/events/EventCalendar';
import BusinessEventOpportunities from '@/components/events/BusinessEventOpportunities';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Handshake, 
  TrendingUp, 
  Users, 
  MapPin,
  Star,
  Trophy,
  Target
} from 'lucide-react';

const Events = () => {
  const eventStats = [
    {
      icon: <Calendar className="h-8 w-8" />,
      title: 'Monthly Events',
      value: '150+',
      description: 'Local events and festivals'
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: 'Business Connections',
      value: '500+',
      description: 'Successful partnerships'
    },
    {
      icon: <Trophy className="h-8 w-8" />,
      title: 'Sponsorship Deals',
      value: 'R2.5M+',
      description: 'Total sponsorship value'
    },
    {
      icon: <Target className="h-8 w-8" />,
      title: 'ROI Average',
      value: '300%',
      description: 'Return on event investment'
    }
  ];

  const eventTypes = [
    {
      icon: '🎪',
      title: 'Festivals',
      description: 'Food, wine, arts, and cultural festivals',
      count: 45
    },
    {
      icon: '🎤',
      title: 'Conferences',
      description: 'Business and industry conferences',
      count: 32
    },
    {
      icon: '🛠️',
      title: 'Workshops',
      description: 'Skills development and training',
      count: 28
    },
    {
      icon: '🤝',
      title: 'Networking',
      description: 'Business networking events',
      count: 56
    },
    {
      icon: '🏛️',
      title: 'Exhibitions',
      description: 'Trade shows and exhibitions',
      count: 24
    },
    {
      icon: '🎵',
      title: 'Entertainment',
      description: 'Concerts and entertainment events',
      count: 38
    }
  ];

  return (
    <Layout>
      <div className="py-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-slate-800">
              Local Events & Business Opportunities
            </h1>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Connect your business with local events, festivals, and networking opportunities. 
              Increase visibility and build valuable partnerships in your community.
            </p>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-10">
            {eventStats.map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center">
                  <div className="p-3 bg-[#007749] bg-opacity-10 rounded-lg text-[#007749] w-fit mx-auto mb-4">
                    {stat.icon}
                  </div>
                  <div className="text-3xl font-bold text-slate-800 mb-2">
                    {stat.value}
                  </div>
                  <div className="font-semibold text-slate-700 mb-1">
                    {stat.title}
                  </div>
                  <div className="text-sm text-slate-500">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Tabs defaultValue="calendar" className="space-y-8">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="calendar">Event Calendar</TabsTrigger>
              <TabsTrigger value="opportunities">Business Opportunities</TabsTrigger>
              <TabsTrigger value="types">Event Types</TabsTrigger>
            </TabsList>

            <TabsContent value="calendar">
              <EventCalendar />
            </TabsContent>

            <TabsContent value="opportunities">
              <BusinessEventOpportunities />
            </TabsContent>

            <TabsContent value="types">
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold mb-4">Event Categories</h2>
                  <p className="text-slate-600">
                    Explore different types of events and find the perfect opportunities for your business
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {eventTypes.map((type, index) => (
                    <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                      <CardHeader>
                        <div className="flex items-center gap-3 mb-2">
                          <div className="text-4xl">{type.icon}</div>
                          <div>
                            <CardTitle className="text-lg">{type.title}</CardTitle>
                            <Badge variant="secondary">{type.count} events</Badge>
                          </div>
                        </div>
                        <CardDescription>{type.description}</CardDescription>
                      </CardHeader>
                    </Card>
                  ))}
                </div>

                <Card className="bg-gradient-to-r from-[#007749] to-[#006739] text-white">
                  <CardContent className="p-8 text-center">
                    <h3 className="text-2xl font-bold mb-4">Host Your Own Event</h3>
                    <p className="text-lg mb-6 opacity-90">
                      Planning an event? List it on our platform and connect with local businesses for sponsorship and partnerships.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold">10K+</div>
                        <div className="text-sm opacity-80">Event Views</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold">500+</div>
                        <div className="text-sm opacity-80">Business Partners</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold">95%</div>
                        <div className="text-sm opacity-80">Success Rate</div>
                      </div>
                    </div>
                    <button className="bg-white text-[#007749] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                      List Your Event
                    </button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Events;
