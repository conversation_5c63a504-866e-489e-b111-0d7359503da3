import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, Mail, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { useEmailNotifications, useEmailNotificationStats } from '@/hooks/useEmailNotifications';
import { toast } from 'sonner';

interface EmailNotification {
  id: string;
  type: string;
  recipient_email: string;
  subject: string;
  data: any;
  status: 'pending' | 'sent' | 'failed';
  attempts: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export const EmailNotificationManager: React.FC = () => {
  const [stats, setStats] = useState({ pending: 0, sent: 0, failed: 0, total: 0 });
  const [notifications, setNotifications] = useState<EmailNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const { 
    processEmailNotifications, 
    getEmailNotifications, 
    retryFailedNotifications 
  } = useEmailNotifications();
  
  const { getStats } = useEmailNotificationStats();

  // Load stats and notifications
  const loadData = async () => {
    setLoading(true);
    try {
      const [statsResult, notificationsResult] = await Promise.all([
        getStats(),
        getEmailNotifications(0, 50) // Get latest 50 notifications
      ]);

      setStats(statsResult);
      setNotifications(notificationsResult.data);
    } catch (error) {
      console.error('Error loading email notification data:', error);
      toast.error('Failed to load email notification data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleProcessEmails = async () => {
    setLoading(true);
    try {
      await processEmailNotifications();
      await loadData(); // Refresh data
    } finally {
      setLoading(false);
    }
  };

  const handleRetryFailed = async () => {
    setLoading(true);
    try {
      await retryFailedNotifications();
      await loadData(); // Refresh data
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      sent: 'default',
      failed: 'destructive'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Email Notifications</h2>
          <p className="text-muted-foreground">
            Monitor and manage automated email notifications
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={handleProcessEmails} 
            disabled={loading}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Process Emails
          </Button>
          {stats.failed > 0 && (
            <Button 
              onClick={handleRetryFailed} 
              disabled={loading}
              variant="outline"
            >
              <AlertCircle className="h-4 w-4 mr-2" />
              Retry Failed ({stats.failed})
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sent</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.sent}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.failed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Notifications</CardTitle>
          <CardDescription>
            Latest email notifications and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="overview">All</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="sent">Sent</TabsTrigger>
              <TabsTrigger value="failed">Failed</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <NotificationsList 
                notifications={notifications} 
                loading={loading}
                getStatusIcon={getStatusIcon}
                getStatusBadge={getStatusBadge}
                formatDate={formatDate}
              />
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              <NotificationsList 
                notifications={notifications.filter(n => n.status === 'pending')} 
                loading={loading}
                getStatusIcon={getStatusIcon}
                getStatusBadge={getStatusBadge}
                formatDate={formatDate}
              />
            </TabsContent>

            <TabsContent value="sent" className="space-y-4">
              <NotificationsList 
                notifications={notifications.filter(n => n.status === 'sent')} 
                loading={loading}
                getStatusIcon={getStatusIcon}
                getStatusBadge={getStatusBadge}
                formatDate={formatDate}
              />
            </TabsContent>

            <TabsContent value="failed" className="space-y-4">
              <NotificationsList 
                notifications={notifications.filter(n => n.status === 'failed')} 
                loading={loading}
                getStatusIcon={getStatusIcon}
                getStatusBadge={getStatusBadge}
                formatDate={formatDate}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

interface NotificationsListProps {
  notifications: EmailNotification[];
  loading: boolean;
  getStatusIcon: (status: string) => React.ReactNode;
  getStatusBadge: (status: string) => React.ReactNode;
  formatDate: (dateString: string) => string;
}

const NotificationsList: React.FC<NotificationsListProps> = ({
  notifications,
  loading,
  getStatusIcon,
  getStatusBadge,
  formatDate
}) => {
  if (loading) {
    return <div className="text-center py-4">Loading notifications...</div>;
  }

  if (notifications.length === 0) {
    return <div className="text-center py-4 text-muted-foreground">No notifications found</div>;
  }

  return (
    <div className="space-y-2">
      {notifications.map((notification) => (
        <div 
          key={notification.id} 
          className="flex items-center justify-between p-4 border rounded-lg"
        >
          <div className="flex items-center space-x-3">
            {getStatusIcon(notification.status)}
            <div>
              <div className="font-medium">{notification.subject}</div>
              <div className="text-sm text-muted-foreground">
                To: {notification.recipient_email} • Type: {notification.type}
              </div>
              {notification.error_message && (
                <div className="text-sm text-red-600 mt-1">
                  Error: {notification.error_message}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="text-sm text-muted-foreground">
              {formatDate(notification.created_at)}
            </div>
            <div className="text-sm text-muted-foreground">
              Attempts: {notification.attempts}
            </div>
            {getStatusBadge(notification.status)}
          </div>
        </div>
      ))}
    </div>
  );
};
