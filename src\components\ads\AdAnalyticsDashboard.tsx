import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  Eye, 
  MousePointer, 
  TrendingUp, 
  DollarSign,
  Calendar,
  Activity
} from 'lucide-react';

interface Ad {
  id: string;
  title: string;
  ad_type: string;
  position: string;
  clicks: number;
  impressions: number;
  budget_total: number | null;
  budget_spent: number;
  status: string;
  start_date: string;
  end_date: string | null;
  created_at: string;
}

interface AdAnalyticsDashboardProps {
  businessId?: string;
}

const AdAnalyticsDashboard = ({ businessId }: AdAnalyticsDashboardProps) => {
  const { data: ads, isLoading } = useQuery({
    queryKey: ['ad-analytics', businessId],
    queryFn: async () => {
      let query = supabase
        .from('ads')
        .select('*')
        .order('created_at', { ascending: false });

      if (businessId) {
        query = query.eq('business_id', businessId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as Ad[];
    },
  });

  if (isLoading) {
    return <div className="p-6">Loading analytics...</div>;
  }

  if (!ads || ads.length === 0) {
    return (
      <div className="text-center py-12">
        <Activity className="h-12 w-12 text-slate-400 mx-auto mb-4" />
        <p className="text-slate-600">No ads found.</p>
      </div>
    );
  }

  const totalImpressions = ads.reduce((sum, ad) => sum + ad.impressions, 0);
  const totalClicks = ads.reduce((sum, ad) => sum + ad.clicks, 0);
  const totalSpent = ads.reduce((sum, ad) => sum + ad.budget_spent, 0);
  const ctr = totalImpressions > 0 ? ((totalClicks / totalImpressions) * 100).toFixed(2) : '0.00';
  const avgCpc = totalClicks > 0 ? (totalSpent / totalClicks / 100).toFixed(2) : '0.00';

  const statusData = ads.reduce((acc, ad) => {
    acc[ad.status] = (acc[ad.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusChartData = Object.entries(statusData).map(([status, count]) => ({
    name: status.charAt(0).toUpperCase() + status.slice(1),
    value: count
  }));

  const performanceData = ads.map(ad => ({
    name: ad.title.substring(0, 20) + (ad.title.length > 20 ? '...' : ''),
    impressions: ad.impressions,
    clicks: ad.clicks,
    ctr: ad.impressions > 0 ? ((ad.clicks / ad.impressions) * 100).toFixed(1) : '0'
  }));

  const COLORS = ['#007749', '#FDB913', '#003087', '#6B7280'];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'active': { color: 'bg-green-100 text-green-800', label: 'Active' },
      'pending': { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      'paused': { color: 'bg-gray-100 text-gray-800', label: 'Paused' },
      'completed': { color: 'bg-blue-100 text-blue-800', label: 'Completed' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Impressions</p>
                <p className="text-2xl font-bold">{totalImpressions.toLocaleString()}</p>
              </div>
              <Eye className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Clicks</p>
                <p className="text-2xl font-bold">{totalClicks.toLocaleString()}</p>
              </div>
              <MousePointer className="h-8 w-8 text-[#007749]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Click-Through Rate</p>
                <p className="text-2xl font-bold">{ctr}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Spent</p>
                <p className="text-2xl font-bold">R{(totalSpent / 100).toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Ad Performance</CardTitle>
            <CardDescription>Impressions and clicks by ad</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="impressions" fill="#007749" name="Impressions" />
                <Bar dataKey="clicks" fill="#FDB913" name="Clicks" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Ad Status Distribution</CardTitle>
            <CardDescription>Current status of all ads</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Ad List */}
      <Card>
        <CardHeader>
          <CardTitle>Ad Campaign Details</CardTitle>
          <CardDescription>Detailed performance metrics for each ad</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {ads.map((ad) => (
              <Card key={ad.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold">{ad.title}</h3>
                      {getStatusBadge(ad.status)}
                      <Badge variant="outline">{ad.position}</Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-slate-600">Impressions</p>
                        <p className="font-medium">{ad.impressions.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-slate-600">Clicks</p>
                        <p className="font-medium">{ad.clicks.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-slate-600">CTR</p>
                        <p className="font-medium">
                          {ad.impressions > 0 ? ((ad.clicks / ad.impressions) * 100).toFixed(2) : '0.00'}%
                        </p>
                      </div>
                      <div>
                        <p className="text-slate-600">Spent</p>
                        <p className="font-medium">R{(ad.budget_spent / 100).toFixed(2)}</p>
                      </div>
                    </div>

                    <div className="mt-2 text-xs text-slate-500">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Started: {new Date(ad.start_date).toLocaleDateString()}
                        {ad.end_date && ` • Ends: ${new Date(ad.end_date).toLocaleDateString()}`}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdAnalyticsDashboard;
