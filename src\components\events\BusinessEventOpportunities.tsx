import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Handshake, 
  DollarSign, 
  Trophy, 
  Users, 
  Calendar,
  MapPin,
  Star,
  Plus,
  Send
} from 'lucide-react';
import { toast } from 'sonner';

interface Event {
  id: string;
  title: string;
  description: string | null;
  event_type: string;
  start_date: string;
  end_date: string | null;
  location: string;
  city: string;
  organizer_name: string | null;
  featured: boolean;
}

interface BusinessEventConnection {
  id: string;
  connection_type: string;
  description: string | null;
  special_offer: string | null;
  discount_code: string | null;
  is_featured: boolean;
  events: Event;
}

const BusinessEventOpportunities = () => {
  const { user } = useAuth();
  const [showConnectionForm, setShowConnectionForm] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [connectionForm, setConnectionForm] = useState({
    connection_type: 'partner',
    description: '',
    special_offer: '',
    discount_code: ''
  });

  // Get user's business listings
  const { data: userBusinesses } = useQuery({
    queryKey: ['user-businesses', user?.id],
    queryFn: async () => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('business_listings')
        .select('id, title')
        .eq('user_id', user.id);

      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  // Get upcoming events for sponsorship opportunities
  const { data: events, isLoading: eventsLoading } = useQuery({
    queryKey: ['sponsorship-events'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .eq('status', 'published')
        .gte('start_date', new Date().toISOString())
        .order('start_date', { ascending: true })
        .limit(20);

      if (error) throw error;
      return data as Event[];
    },
  });

  // Get user's existing event connections
  const { data: connections, refetch: refetchConnections } = useQuery({
    queryKey: ['business-event-connections', user?.id],
    queryFn: async () => {
      if (!user || !userBusinesses || userBusinesses.length === 0) return [];

      const businessIds = userBusinesses.map(b => b.id);
      const { data, error } = await supabase
        .from('business_event_connections')
        .select(`
          *,
          events (
            id,
            title,
            description,
            event_type,
            start_date,
            end_date,
            location,
            city,
            organizer_name,
            featured
          )
        `)
        .in('business_id', businessIds)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as BusinessEventConnection[];
    },
    enabled: !!user && !!userBusinesses && userBusinesses.length > 0,
  });

  const handleCreateConnection = async () => {
    if (!user || !userBusinesses || userBusinesses.length === 0 || !selectedEvent) {
      toast.error('Please ensure you have a business listing and select an event');
      return;
    }

    try {
      const { error } = await supabase
        .from('business_event_connections')
        .insert({
          business_id: userBusinesses[0].id, // Use first business for now
          event_id: selectedEvent.id,
          connection_type: connectionForm.connection_type,
          description: connectionForm.description || null,
          special_offer: connectionForm.special_offer || null,
          discount_code: connectionForm.discount_code || null
        });

      if (error) throw error;

      toast.success('Event connection created successfully!');
      setShowConnectionForm(false);
      setSelectedEvent(null);
      setConnectionForm({
        connection_type: 'partner',
        description: '',
        special_offer: '',
        discount_code: ''
      });
      refetchConnections();
    } catch (error) {
      console.error('Error creating connection:', error);
      toast.error('Failed to create event connection');
    }
  };

  const connectionTypes = [
    { value: 'sponsor', label: 'Sponsor', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'exhibitor', label: 'Exhibitor', icon: <Trophy className="h-4 w-4" /> },
    { value: 'vendor', label: 'Vendor', icon: <Handshake className="h-4 w-4" /> },
    { value: 'partner', label: 'Partner', icon: <Users className="h-4 w-4" /> },
    { value: 'speaker', label: 'Speaker', icon: <Star className="h-4 w-4" /> }
  ];

  const getConnectionTypeColor = (type: string) => {
    switch (type) {
      case 'sponsor':
        return 'bg-purple-100 text-purple-800';
      case 'exhibitor':
        return 'bg-blue-100 text-blue-800';
      case 'vendor':
        return 'bg-green-100 text-green-800';
      case 'partner':
        return 'bg-orange-100 text-orange-800';
      case 'speaker':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Handshake className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Sign in to explore event opportunities for your business</p>
        </CardContent>
      </Card>
    );
  }

  if (!userBusinesses || userBusinesses.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Handshake className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600 mb-2">No business listings found</p>
          <p className="text-sm text-slate-500">
            Create a business listing to explore event opportunities
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Your Event Connections */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Handshake className="h-5 w-5 text-[#007749]" />
                Your Event Connections
              </CardTitle>
              <CardDescription>
                Manage your business connections with local events
              </CardDescription>
            </div>
            <Button
              onClick={() => setShowConnectionForm(true)}
              className="bg-[#007749] hover:bg-[#006739]"
            >
              <Plus className="h-4 w-4 mr-2" />
              Connect to Event
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {connections && connections.length > 0 ? (
            <div className="space-y-4">
              {connections.map((connection) => (
                <Card key={connection.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{connection.events.title}</h3>
                          <Badge className={getConnectionTypeColor(connection.connection_type)}>
                            {connectionTypes.find(t => t.value === connection.connection_type)?.icon}
                            <span className="ml-1 capitalize">{connection.connection_type}</span>
                          </Badge>
                          {connection.is_featured && (
                            <Badge className="bg-[#FDB913] text-black">Featured</Badge>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-600 mb-3">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {new Date(connection.events.start_date).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {connection.events.city}
                          </div>
                        </div>

                        {connection.description && (
                          <p className="text-slate-700 mb-2">{connection.description}</p>
                        )}

                        {connection.special_offer && (
                          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-2">
                            <p className="text-sm font-medium text-green-800 mb-1">Special Offer:</p>
                            <p className="text-sm text-green-700">{connection.special_offer}</p>
                            {connection.discount_code && (
                              <p className="text-sm text-green-600 mt-1">
                                Code: <span className="font-mono font-bold">{connection.discount_code}</span>
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Handshake className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600 mb-2">No event connections yet</p>
              <p className="text-sm text-slate-500">
                Connect your business with local events to increase visibility
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Available Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-[#007749]" />
            Available Event Opportunities
          </CardTitle>
          <CardDescription>
            Upcoming events looking for business partners and sponsors
          </CardDescription>
        </CardHeader>
        <CardContent>
          {eventsLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-24"></div>
              ))}
            </div>
          ) : events && events.length > 0 ? (
            <div className="space-y-4">
              {events.slice(0, 10).map((event) => (
                <Card key={event.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold">{event.title}</h3>
                          {event.featured && (
                            <Badge className="bg-[#FDB913] text-black">Featured</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-slate-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {new Date(event.start_date).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {event.city}
                          </div>
                          {event.organizer_name && (
                            <span>by {event.organizer_name}</span>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedEvent(event);
                          setShowConnectionForm(true);
                        }}
                      >
                        Connect
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600">No upcoming events available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Connection Form Modal */}
      {showConnectionForm && selectedEvent && (
        <Card className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <CardHeader className="px-0 pt-0">
              <CardTitle>Connect to {selectedEvent.title}</CardTitle>
              <CardDescription>
                Create a connection between your business and this event
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0 space-y-4">
              <div>
                <label className="text-sm font-medium">Connection Type</label>
                <Select 
                  value={connectionForm.connection_type} 
                  onValueChange={(value) => setConnectionForm(prev => ({ ...prev, connection_type: value }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {connectionTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          {type.icon}
                          {type.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={connectionForm.description}
                  onChange={(e) => setConnectionForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your involvement or what you'll offer..."
                  rows={3}
                  className="mt-1"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Special Offer (Optional)</label>
                <Textarea
                  value={connectionForm.special_offer}
                  onChange={(e) => setConnectionForm(prev => ({ ...prev, special_offer: e.target.value }))}
                  placeholder="Special offer for event attendees..."
                  rows={2}
                  className="mt-1"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Discount Code (Optional)</label>
                <Input
                  value={connectionForm.discount_code}
                  onChange={(e) => setConnectionForm(prev => ({ ...prev, discount_code: e.target.value }))}
                  placeholder="e.g., EVENT20"
                  className="mt-1"
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowConnectionForm(false);
                    setSelectedEvent(null);
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateConnection}
                  className="flex-1 bg-[#007749] hover:bg-[#006739]"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Create Connection
                </Button>
              </div>
            </CardContent>
          </div>
        </Card>
      )}
    </div>
  );
};

export default BusinessEventOpportunities;
