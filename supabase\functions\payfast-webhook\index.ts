
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { createHash } from "https://deno.land/std@0.190.0/hash/mod.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[PAYFAST-WEBHOOK] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Webhook received");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const formData = await req.formData();
    const data: Record<string, string> = {};
    
    for (const [key, value] of formData.entries()) {
      data[key] = value.toString();
    }

    logStep("Webhook data received", data);

    // Verify signature
    const signature = data.signature;
    delete data.signature;
    
    const passphrase = Deno.env.get("PAYFAST_PASSPHRASE");
    const generateSignature = (data: any, passphrase?: string) => {
      const params = Object.keys(data)
        .filter(key => data[key] !== "" && data[key] !== null && data[key] !== undefined)
        .sort()
        .map(key => `${key}=${encodeURIComponent(data[key])}`)
        .join("&");
      
      const signatureString = passphrase ? `${params}&passphrase=${passphrase}` : params;
      return createHash("md5").update(signatureString).toString();
    };

    const expectedSignature = generateSignature(data, passphrase);
    
    if (signature !== expectedSignature) {
      logStep("Invalid signature", { received: signature, expected: expectedSignature });
      return new Response("Invalid signature", { status: 400 });
    }

    const orderId = data.m_payment_id;
    const paymentStatus = data.payment_status;
    
    logStep("Processing payment", { orderId, paymentStatus });

    // Update subscription based on payment status
    let subscriptionStatus = "pending";
    if (paymentStatus === "COMPLETE") {
      subscriptionStatus = "active";
    } else if (paymentStatus === "CANCELLED") {
      subscriptionStatus = "cancelled";
    }

    const { error: updateError } = await supabaseClient
      .from("subscriptions")
      .update({
        status: subscriptionStatus,
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        updated_at: new Date().toISOString()
      })
      .eq("stripe_subscription_id", orderId);

    if (updateError) {
      logStep("Error updating subscription", updateError);
      throw new Error("Failed to update subscription");
    }

    // Update business listing if payment is complete
    if (paymentStatus === "COMPLETE") {
      const { data: subscription } = await supabaseClient
        .from("subscriptions")
        .select("business_id, tier")
        .eq("stripe_subscription_id", orderId)
        .single();

      if (subscription) {
        const tierSettings = {
          silver: { featured: false, sponsored: false, verified: false, priority_score: 10 },
          gold: { featured: true, sponsored: false, verified: true, priority_score: 20 },
          platinum: { featured: true, sponsored: true, verified: true, priority_score: 30 }
        };

        const settings = tierSettings[subscription.tier as keyof typeof tierSettings];
        
        await supabaseClient
          .from("business_listings")
          .update({
            subscription_tier: subscription.tier,
            subscription_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            ...settings
          })
          .eq("id", subscription.business_id);

        logStep("Business listing updated", { businessId: subscription.business_id, tier: subscription.tier });
      }
    }

    logStep("Webhook processed successfully");
    return new Response("OK", { status: 200 });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
