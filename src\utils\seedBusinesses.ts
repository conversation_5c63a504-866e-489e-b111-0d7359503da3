import { supabase } from '@/integrations/supabase/client';

export const seedSampleBusinesses = async () => {
  try {
    console.log('Starting to seed sample businesses...');

    // First, get some categories
    const { data: categories, error: catError } = await supabase
      .from('categories')
      .select('id, name, slug')
      .limit(10);

    if (catError) {
      console.error('Error fetching categories:', catError);
      throw catError;
    }

    if (!categories || categories.length === 0) {
      console.log('No categories found');
      return;
    }

    console.log('Found categories:', categories);

    // Find specific categories by slug
    const cafeCategory = categories.find(cat => cat.slug === 'cafe') || categories[0];
    const techCategory = categories.find(cat => cat.slug === 'technology') || categories[1];
    const accommodationCategory = categories.find(cat => cat.slug === 'accommodation') || categories[2];
    const professionalCategory = categories.find(cat => cat.slug === 'professional-services') || categories[3];
    const automotiveCategory = categories.find(cat => cat.slug === 'automotive') || categories[4];

    // Sample business data
    const sampleBusinesses = [
      {
        title: "Cape Town Coffee Roasters",
        description: "Premium coffee roasting and café serving the finest South African beans",
        category_id: cafeCategory?.id,
        full_address: "123 Long Street, Cape Town, Western Cape",
        city: "Cape Town",
        phone: "+27 21 123 4567",
        website: "https://capetowncoffee.co.za",
        price_range: "R50-R150",
        status: "approved",
        featured: true,
        subscription_tier: "gold",
        premium_placement_score: 80,
        verified: true,
        images: [
          "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
        ],
        logo_url: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
      },
      {
        title: "Johannesburg Tech Solutions",
        description: "Leading IT services and software development company in Johannesburg",
        category_id: techCategory?.id,
        full_address: "456 Sandton Drive, Sandton, Gauteng",
        city: "Johannesburg",
        phone: "+27 11 987 6543",
        website: "https://jburgtech.co.za",
        price_range: "R1000-R5000",
        status: "approved",
        featured: false,
        subscription_tier: "silver",
        premium_placement_score: 60,
        verified: true,
        images: [
          "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"
        ]
      },
      {
        title: "Durban Beachfront Hotel",
        description: "Luxury accommodation with stunning ocean views in Durban",
        category_id: accommodationCategory?.id,
        full_address: "789 Marine Parade, Durban, KwaZulu-Natal",
        city: "Durban",
        phone: "+27 31 555 7890",
        website: "https://durbanbeachfront.co.za",
        price_range: "R800-R2500",
        status: "approved",
        featured: true,
        subscription_tier: "platinum",
        premium_placement_score: 100,
        verified: true,
        images: [
          "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
        ]
      },
      {
        title: "Pretoria Legal Services",
        description: "Comprehensive legal services for individuals and businesses",
        category_id: professionalCategory?.id,
        full_address: "321 Church Street, Pretoria, Gauteng",
        city: "Pretoria",
        phone: "+27 12 345 6789",
        website: "https://pretorialegal.co.za",
        price_range: "R500-R3000",
        status: "approved",
        featured: false,
        subscription_tier: "free",
        premium_placement_score: 0,
        verified: false,
        images: [
          "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
        ]
      },
      {
        title: "Port Elizabeth Auto Repair",
        description: "Trusted automotive repair and maintenance services",
        category_id: automotiveCategory?.id,
        full_address: "654 Main Road, Port Elizabeth, Eastern Cape",
        city: "Port Elizabeth",
        phone: "+27 41 123 4567",
        website: "https://peautorepair.co.za",
        price_range: "R200-R1500",
        status: "approved",
        featured: false,
        subscription_tier: "silver",
        premium_placement_score: 60,
        verified: true,
        images: [
          "https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
        ]
      }
    ];

    // Get current user or create a dummy user ID
    const { data: { user } } = await supabase.auth.getUser();
    let userId = user?.id;

    if (!userId) {
      // Use a valid UUID format for dummy user
      userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
    }

    // Insert sample businesses
    for (const business of sampleBusinesses) {
      const { data, error } = await supabase
        .from('business_listings')
        .insert({
          ...business,
          user_id: userId
        })
        .select();

      if (error) {
        console.error('Error inserting business:', business.title, error);
      } else {
        console.log(`Inserted business: ${business.title}`, data);
      }
    }

    console.log('Sample businesses seeded successfully!');
  } catch (error) {
    console.error('Error seeding businesses:', error);
  }
};
