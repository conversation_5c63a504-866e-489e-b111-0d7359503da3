// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tcqltnxugtzludqhngrs.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRjcWx0bnh1Z3R6bHVkcWhuZ3JzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NzYyNzMsImV4cCI6MjA2NzU1MjI3M30.Nxb-qoBPa0hKKjI8cf79jbFrooLD7GwTYR_sdh1MPs4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});