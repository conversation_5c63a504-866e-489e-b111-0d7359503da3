-- Create events table
CREATE TABLE public.events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  event_type TEXT NOT NULL CHECK (event_type IN ('festival', 'conference', 'workshop', 'networking', 'exhibition', 'concert', 'sports', 'community', 'other')),
  category TEXT, -- Related business category
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  location TEXT NOT NULL,
  venue_name TEXT,
  address TEXT,
  city TEXT NOT NULL,
  province TEXT,
  coordinates POINT, -- Geographic coordinates
  organizer_name TEXT,
  organizer_contact TEXT,
  website_url TEXT,
  ticket_url TEXT,
  price_range TEXT,
  capacity INTEGER,
  images TEXT[],
  tags TEXT[],
  featured BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'published' CHECK (status IN ('draft', 'published', 'cancelled', 'completed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create event sponsorships table
CREATE TABLE public.event_sponsorships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  sponsorship_type TEXT NOT NULL CHECK (sponsorship_type IN ('title', 'gold', 'silver', 'bronze', 'exhibitor', 'partner')),
  sponsorship_amount DECIMAL(10,2),
  benefits JSONB, -- Sponsorship benefits and perks
  logo_placement TEXT, -- Where logo will be displayed
  booth_number TEXT, -- For exhibitions
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'active', 'completed', 'cancelled')),
  contract_start_date TIMESTAMP WITH TIME ZONE,
  contract_end_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create business event connections (for cross-promotion)
CREATE TABLE public.business_event_connections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  connection_type TEXT NOT NULL CHECK (connection_type IN ('sponsor', 'exhibitor', 'vendor', 'partner', 'attendee', 'speaker')),
  description TEXT,
  special_offer TEXT, -- Special offers for event attendees
  discount_code TEXT,
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(business_id, event_id, connection_type)
);

-- Create event attendees/interests table
CREATE TABLE public.event_interests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  interest_type TEXT NOT NULL CHECK (interest_type IN ('interested', 'attending', 'maybe', 'not_interested')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(event_id, user_id)
);

-- Create event recommendations table
CREATE TABLE public.event_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  business_id UUID REFERENCES public.business_listings(id) ON DELETE SET NULL,
  recommendation_score DECIMAL(5,2) NOT NULL,
  recommendation_reason TEXT,
  is_viewed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample events
INSERT INTO public.events (title, description, event_type, category, start_date, end_date, location, city, province, organizer_name, website_url, price_range, images, tags, featured) VALUES
('Cape Town Food & Wine Festival', 'Annual celebration of South African cuisine and wine culture', 'festival', 'restaurant', '2024-03-15 10:00:00+02', '2024-03-17 22:00:00+02', 'V&A Waterfront', 'Cape Town', 'Western Cape', 'Cape Town Tourism', 'https://foodandwine.co.za', 'R150-R500', ARRAY['https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3'], ARRAY['food', 'wine', 'festival', 'cape town'], true),

('Johannesburg Business Expo', 'Premier business networking and exhibition event', 'exhibition', 'professional-services', '2024-04-20 09:00:00+02', '2024-04-22 17:00:00+02', 'Sandton Convention Centre', 'Johannesburg', 'Gauteng', 'Business Events SA', 'https://businessexpo.co.za', 'R200-R1000', ARRAY['https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3'], ARRAY['business', 'networking', 'exhibition'], true),

('Durban Tech Conference', 'Leading technology conference in KwaZulu-Natal', 'conference', 'technology', '2024-05-10 08:00:00+02', '2024-05-12 18:00:00+02', 'Durban ICC', 'Durban', 'KwaZulu-Natal', 'Tech SA', 'https://durbantech.co.za', 'R500-R2000', ARRAY['https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3'], ARRAY['technology', 'conference', 'innovation'], true),

('Pretoria Arts & Crafts Market', 'Monthly artisan market featuring local creators', 'community', 'retail', '2024-02-25 08:00:00+02', '2024-02-25 16:00:00+02', 'Union Buildings Gardens', 'Pretoria', 'Gauteng', 'Pretoria Arts Council', 'https://pretoriaarts.co.za', 'Free entry', ARRAY['https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3'], ARRAY['arts', 'crafts', 'market', 'local'], false),

('Port Elizabeth Music Festival', 'Three-day music festival featuring local and international artists', 'concert', 'entertainment', '2024-06-01 18:00:00+02', '2024-06-03 23:00:00+02', 'Bayworld Complex', 'Port Elizabeth', 'Eastern Cape', 'PE Entertainment', 'https://pemusicfest.co.za', 'R300-R1500', ARRAY['https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3'], ARRAY['music', 'festival', 'entertainment'], false);

-- Insert sample sponsorships
INSERT INTO public.event_sponsorships (event_id, business_id, sponsorship_type, sponsorship_amount, benefits, status) VALUES
((SELECT id FROM public.events WHERE title = 'Cape Town Food & Wine Festival'), 
 (SELECT id FROM public.business_listings WHERE title = 'Cape Town Coffee Roasters' LIMIT 1), 
 'gold', 25000.00, 
 '{"logo_placement": "main_stage", "booth_space": "premium", "marketing_mentions": 5}', 
 'active'),

((SELECT id FROM public.events WHERE title = 'Johannesburg Business Expo'), 
 (SELECT id FROM public.business_listings WHERE title = 'Johannesburg Tech Solutions' LIMIT 1), 
 'exhibitor', 15000.00, 
 '{"booth_space": "standard", "networking_access": true, "speaker_opportunity": true}', 
 'active');

-- Insert sample business-event connections
INSERT INTO public.business_event_connections (business_id, event_id, connection_type, description, special_offer, discount_code) VALUES
((SELECT id FROM public.business_listings WHERE title = 'Cape Town Coffee Roasters' LIMIT 1),
 (SELECT id FROM public.events WHERE title = 'Cape Town Food & Wine Festival'),
 'vendor', 'Premium coffee station at the festival', '20% off all coffee products for festival attendees', 'FESTIVAL20'),

((SELECT id FROM public.business_listings WHERE title = 'Durban Beachfront Hotel' LIMIT 1),
 (SELECT id FROM public.events WHERE title = 'Durban Tech Conference'),
 'partner', 'Official accommodation partner', 'Special conference rates starting from R800/night', 'TECHCONF2024');

-- Function to recommend events to users based on their preferences
CREATE OR REPLACE FUNCTION recommend_events_for_user(user_id_param UUID)
RETURNS TABLE(
  event_id UUID,
  event_title TEXT,
  recommendation_score DECIMAL,
  recommendation_reason TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id as event_id,
    e.title as event_title,
    CASE 
      WHEN cp.preferences->>'preferredCategories' IS NOT NULL 
        AND e.category = ANY(string_to_array(cp.preferences->>'preferredCategories', ','))
      THEN 90.0
      WHEN cp.location IS NOT NULL AND e.city ILIKE '%' || cp.location || '%'
      THEN 80.0
      WHEN e.featured = true
      THEN 70.0
      ELSE 50.0
    END as recommendation_score,
    CASE 
      WHEN cp.preferences->>'preferredCategories' IS NOT NULL 
        AND e.category = ANY(string_to_array(cp.preferences->>'preferredCategories', ','))
      THEN 'Matches your business interests'
      WHEN cp.location IS NOT NULL AND e.city ILIKE '%' || cp.location || '%'
      THEN 'Event in your area'
      WHEN e.featured = true
      THEN 'Featured event'
      ELSE 'Popular event'
    END as recommendation_reason
  FROM public.events e
  LEFT JOIN public.customer_profiles cp ON cp.user_id = user_id_param
  WHERE e.status = 'published' 
    AND e.start_date > NOW()
  ORDER BY recommendation_score DESC, e.start_date ASC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_sponsorships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_event_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_interests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_recommendations ENABLE ROW LEVEL SECURITY;

-- Events policies
CREATE POLICY "Anyone can view published events" ON public.events
  FOR SELECT USING (status = 'published');

-- Event sponsorships policies
CREATE POLICY "Business owners can view their sponsorships" ON public.event_sponsorships
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Business owners can create sponsorship applications" ON public.event_sponsorships
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Business event connections policies
CREATE POLICY "Anyone can view business event connections" ON public.business_event_connections
  FOR SELECT USING (true);

CREATE POLICY "Business owners can manage their event connections" ON public.business_event_connections
  FOR ALL USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Event interests policies
CREATE POLICY "Users can manage their event interests" ON public.event_interests
  FOR ALL USING (auth.uid() = user_id);

-- Event recommendations policies
CREATE POLICY "Users can view their event recommendations" ON public.event_recommendations
  FOR SELECT USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_events_start_date ON public.events(start_date);
CREATE INDEX idx_events_city ON public.events(city);
CREATE INDEX idx_events_category ON public.events(category);
CREATE INDEX idx_events_status ON public.events(status);
CREATE INDEX idx_event_sponsorships_business_id ON public.event_sponsorships(business_id);
CREATE INDEX idx_event_sponsorships_event_id ON public.event_sponsorships(event_id);
CREATE INDEX idx_business_event_connections_business_id ON public.business_event_connections(business_id);
CREATE INDEX idx_business_event_connections_event_id ON public.business_event_connections(event_id);
CREATE INDEX idx_event_interests_user_id ON public.event_interests(user_id);
CREATE INDEX idx_event_interests_event_id ON public.event_interests(event_id);
CREATE INDEX idx_event_recommendations_user_id ON public.event_recommendations(user_id);
