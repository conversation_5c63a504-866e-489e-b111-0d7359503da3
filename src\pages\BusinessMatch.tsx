
import React, { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Search, ArrowRight, User, Save } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import BusinessMatchResults from "@/components/business-match/BusinessMatchResults";

type Question = {
  id: number;
  question: string;
  type: "text" | "select" | "textarea";
  options?: string[];
  weight?: number;
  placeholder?: string;
};

interface CustomerProfile {
  id: string;
  name: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  preferences: any;
}

interface Questionnaire {
  id: string;
  title: string;
  description: string | null;
  questions: Question[];
}

const BusinessMatch = () => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [questionnaire, setQuestionnaire] = useState<Questionnaire | null>(null);
  const [customerProfile, setCustomerProfile] = useState<CustomerProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadQuestionnaire();
    if (user) {
      loadCustomerProfile();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  const loadQuestionnaire = async () => {
    try {
      const { data, error } = await supabase
        .from('match_questionnaires')
        .select('*')
        .eq('is_active', true)
        .eq('category', 'general')
        .single();

      if (error) throw error;

      setQuestionnaire({
        id: data.id,
        title: data.title,
        description: data.description,
        questions: data.questions as Question[]
      });
    } catch (error) {
      console.error('Error loading questionnaire:', error);
      toast({
        title: "Error",
        description: "Failed to load questionnaire. Please try again.",
        variant: "destructive"
      });
    }
  };

  const loadCustomerProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('customer_profiles')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        throw error;
      }

      if (data) {
        setCustomerProfile(data);
        // Pre-fill answers from profile preferences
        if (data.preferences) {
          setAnswers(data.preferences.lastAnswers || {});
        }
      }
    } catch (error) {
      console.error('Error loading customer profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswer = (questionId: number, answer: string) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: answer,
    }));
  };

  const saveMatchSession = async () => {
    if (!questionnaire) return;

    try {
      const sessionData = {
        user_id: user?.id || null,
        customer_profile_id: customerProfile?.id || null,
        questionnaire_id: questionnaire.id,
        answers: answers,
        session_data: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        }
      };

      const { error } = await supabase
        .from('match_sessions')
        .insert(sessionData);

      if (error) throw error;

      // Update customer profile with latest answers
      if (user && customerProfile) {
        await supabase
          .from('customer_profiles')
          .update({
            preferences: {
              ...customerProfile.preferences,
              lastAnswers: answers,
              lastMatchDate: new Date().toISOString()
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', customerProfile.id);
      }
    } catch (error) {
      console.error('Error saving match session:', error);
    }
  };

  const handleNext = async () => {
    if (!questionnaire) return;

    if (currentStep < questionnaire.questions.length - 1) {
      setCurrentStep((prev) => prev + 1);
    } else {
      // Process final submission
      await saveMatchSession();
      setShowResults(true);
      toast({
        title: "Finding your perfect business match",
        description: "Processing your preferences with our AI algorithm...",
      });
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const createCustomerProfile = async (profileData: any) => {
    try {
      const { data, error } = await supabase
        .from('customer_profiles')
        .insert({
          user_id: user?.id,
          ...profileData,
          preferences: { lastAnswers: answers }
        })
        .select()
        .single();

      if (error) throw error;

      setCustomerProfile(data);
      setShowProfileForm(false);
      toast({
        title: "Profile created",
        description: "Your profile has been saved for better matching.",
      });
    } catch (error) {
      console.error('Error creating profile:', error);
      toast({
        title: "Error",
        description: "Failed to create profile. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="py-12">
          <div className="container mx-auto px-4 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#007749] mx-auto"></div>
            <p className="mt-4 text-slate-600">Loading questionnaire...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!questionnaire) {
    return (
      <Layout>
        <div className="py-12">
          <div className="container mx-auto px-4 text-center">
            <p className="text-slate-600">Unable to load questionnaire. Please try again later.</p>
          </div>
        </div>
      </Layout>
    );
  }

  const currentQuestion = questionnaire.questions[currentStep];

  if (showResults) {
    return (
      <Layout>
        <BusinessMatchResults answers={answers} />
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="mb-8 text-center">
              <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Find Your Perfect Business Match
              </h1>
              <p className="text-slate-600">
                Answer a few questions and our AI will recommend businesses tailored to your needs
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5 text-[#007749]" />
                  Business Match
                </CardTitle>
                <CardDescription>
                  Step {currentStep + 1} of {questions.length}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-3">{currentQuestion.question}</h3>
                  
                  {currentQuestion.type === "text" && (
                    <Input
                      placeholder="Type your answer here"
                      value={answers[currentQuestion.id] || ""}
                      onChange={(e) => handleAnswer(currentQuestion.id, e.target.value)}
                    />
                  )}
                  
                  {currentQuestion.type === "textarea" && (
                    <Textarea
                      placeholder="Type your answer here"
                      value={answers[currentQuestion.id] || ""}
                      onChange={(e) => handleAnswer(currentQuestion.id, e.target.value)}
                      rows={4}
                    />
                  )}
                  
                  {currentQuestion.type === "select" && currentQuestion.options && (
                    <div className="grid grid-cols-2 gap-3">
                      {currentQuestion.options.map((option) => (
                        <Button
                          key={option}
                          variant={answers[currentQuestion.id] === option ? "default" : "outline"}
                          className={answers[currentQuestion.id] === option ? "bg-[#007749] hover:bg-[#006739]" : ""}
                          onClick={() => handleAnswer(currentQuestion.id, option)}
                        >
                          {option}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
                
                <div className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={handleBack}
                    disabled={currentStep === 0}
                  >
                    Back
                  </Button>
                  
                  <Button 
                    onClick={handleNext}
                    className="bg-[#007749] hover:bg-[#006739]"
                    disabled={!answers[currentQuestion.id]}
                  >
                    {currentStep < questions.length - 1 ? (
                      <span className="flex items-center">Next <ArrowRight className="ml-2 h-4 w-4" /></span>
                    ) : (
                      "Get Recommendations"
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BusinessMatch;
