
import React from "react";
import Navbar from "./Navbar";
import Footer from "./Footer";
import AIGuideButton from "../ai-guide/AIGuideButton";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow">{children}</main>
      <Footer />
      <AIGuideButton />
    </div>
  );
};

export default Layout;
