-- Enhance existing ads table with additional fields
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS campaign_name TEXT;
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS target_audience JSONB DEFAULT '{}';
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS keywords TEXT[];
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS ctr DECIMAL(5,2) DEFAULT 0.00;
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS cost_per_click DECIMAL(8,2) DEFAULT 0.00;
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 1;
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id);
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- Create ad click tracking table for detailed analytics
CREATE TABLE public.ad_clicks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_id UUID REFERENCES public.ads(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  page_url TEXT,
  session_id TEXT,
  country TEXT,
  city TEXT,
  device_type TEXT, -- 'desktop', 'mobile', 'tablet'
  browser TEXT,
  clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ad impression tracking table
CREATE TABLE public.ad_impressions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_id UUID REFERENCES public.ads(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  page_url TEXT,
  session_id TEXT,
  country TEXT,
  city TEXT,
  device_type TEXT,
  browser TEXT,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  view_duration INTEGER DEFAULT 0 -- in seconds
);

-- Create ad campaigns table for better organization
CREATE TABLE public.ad_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  budget_total INTEGER NOT NULL, -- in cents
  budget_spent INTEGER DEFAULT 0,
  budget_daily INTEGER, -- daily budget limit
  target_audience JSONB DEFAULT '{}',
  keywords TEXT[],
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add campaign_id to ads table
ALTER TABLE public.ads ADD COLUMN IF NOT EXISTS campaign_id UUID REFERENCES public.ad_campaigns(id) ON DELETE SET NULL;

-- Create ad performance summary table (for faster queries)
CREATE TABLE public.ad_performance_summary (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_id UUID REFERENCES public.ads(id) ON DELETE CASCADE UNIQUE,
  date DATE NOT NULL,
  impressions INTEGER DEFAULT 0,
  clicks INTEGER DEFAULT 0,
  cost INTEGER DEFAULT 0, -- in cents
  conversions INTEGER DEFAULT 0,
  ctr DECIMAL(5,2) DEFAULT 0.00,
  cpc DECIMAL(8,2) DEFAULT 0.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(ad_id, date)
);

-- Create ad placement rules table
CREATE TABLE public.ad_placement_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  position TEXT NOT NULL,
  max_ads INTEGER DEFAULT 3,
  rotation_interval INTEGER DEFAULT 30, -- seconds
  priority_weight DECIMAL(3,2) DEFAULT 1.0,
  page_types TEXT[], -- ['homepage', 'business_listing', 'category', 'search']
  device_types TEXT[], -- ['desktop', 'mobile', 'tablet']
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default placement rules
INSERT INTO public.ad_placement_rules (position, max_ads, rotation_interval, page_types, device_types) VALUES
('top', 3, 30, ARRAY['homepage', 'business_listing', 'category', 'search'], ARRAY['desktop', 'mobile', 'tablet']),
('middle', 2, 45, ARRAY['business_listing', 'category', 'search'], ARRAY['desktop', 'mobile', 'tablet']),
('bottom', 3, 60, ARRAY['homepage', 'business_listing', 'category'], ARRAY['desktop', 'mobile', 'tablet']),
('sidebar', 4, 20, ARRAY['business_listing', 'category', 'search'], ARRAY['desktop', 'tablet']);

-- Enhanced function to track ad clicks with detailed info
CREATE OR REPLACE FUNCTION track_ad_click(
  ad_id_param UUID,
  user_id_param UUID DEFAULT NULL,
  ip_address_param INET DEFAULT NULL,
  user_agent_param TEXT DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL,
  page_url_param TEXT DEFAULT NULL,
  session_id_param TEXT DEFAULT NULL
) RETURNS void AS $$
DECLARE
  device_type_val TEXT;
  browser_val TEXT;
BEGIN
  -- Extract device type from user agent
  IF user_agent_param IS NOT NULL THEN
    IF user_agent_param ILIKE '%mobile%' OR user_agent_param ILIKE '%android%' OR user_agent_param ILIKE '%iphone%' THEN
      device_type_val := 'mobile';
    ELSIF user_agent_param ILIKE '%tablet%' OR user_agent_param ILIKE '%ipad%' THEN
      device_type_val := 'tablet';
    ELSE
      device_type_val := 'desktop';
    END IF;

    -- Extract browser
    IF user_agent_param ILIKE '%chrome%' THEN
      browser_val := 'Chrome';
    ELSIF user_agent_param ILIKE '%firefox%' THEN
      browser_val := 'Firefox';
    ELSIF user_agent_param ILIKE '%safari%' THEN
      browser_val := 'Safari';
    ELSIF user_agent_param ILIKE '%edge%' THEN
      browser_val := 'Edge';
    ELSE
      browser_val := 'Other';
    END IF;
  END IF;

  -- Insert detailed click record
  INSERT INTO public.ad_clicks (
    ad_id, user_id, ip_address, user_agent, referrer, page_url, 
    session_id, device_type, browser
  ) VALUES (
    ad_id_param, user_id_param, ip_address_param, user_agent_param, 
    referrer_param, page_url_param, session_id_param, device_type_val, browser_val
  );

  -- Update ad click count
  UPDATE public.ads 
  SET clicks = clicks + 1,
      updated_at = NOW()
  WHERE id = ad_id_param;

  -- Update campaign budget spent (simplified - in real app would be more complex)
  UPDATE public.ad_campaigns 
  SET budget_spent = budget_spent + 100 -- 1 rand per click for demo
  WHERE id = (SELECT campaign_id FROM public.ads WHERE id = ad_id_param);

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enhanced function to track ad impressions
CREATE OR REPLACE FUNCTION track_ad_impression(
  ad_id_param UUID,
  user_id_param UUID DEFAULT NULL,
  ip_address_param INET DEFAULT NULL,
  user_agent_param TEXT DEFAULT NULL,
  page_url_param TEXT DEFAULT NULL,
  session_id_param TEXT DEFAULT NULL,
  view_duration_param INTEGER DEFAULT 0
) RETURNS void AS $$
DECLARE
  device_type_val TEXT;
  browser_val TEXT;
BEGIN
  -- Extract device type and browser (same logic as click tracking)
  IF user_agent_param IS NOT NULL THEN
    IF user_agent_param ILIKE '%mobile%' OR user_agent_param ILIKE '%android%' OR user_agent_param ILIKE '%iphone%' THEN
      device_type_val := 'mobile';
    ELSIF user_agent_param ILIKE '%tablet%' OR user_agent_param ILIKE '%ipad%' THEN
      device_type_val := 'tablet';
    ELSE
      device_type_val := 'desktop';
    END IF;

    IF user_agent_param ILIKE '%chrome%' THEN
      browser_val := 'Chrome';
    ELSIF user_agent_param ILIKE '%firefox%' THEN
      browser_val := 'Firefox';
    ELSIF user_agent_param ILIKE '%safari%' THEN
      browser_val := 'Safari';
    ELSIF user_agent_param ILIKE '%edge%' THEN
      browser_val := 'Edge';
    ELSE
      browser_val := 'Other';
    END IF;
  END IF;

  -- Insert detailed impression record
  INSERT INTO public.ad_impressions (
    ad_id, user_id, ip_address, user_agent, page_url, 
    session_id, device_type, browser, view_duration
  ) VALUES (
    ad_id_param, user_id_param, ip_address_param, user_agent_param, 
    page_url_param, session_id_param, device_type_val, browser_val, view_duration_param
  );

  -- Update ad impression count
  UPDATE public.ads 
  SET impressions = impressions + 1,
      updated_at = NOW()
  WHERE id = ad_id_param;

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate and update CTR
CREATE OR REPLACE FUNCTION update_ad_ctr(ad_id_param UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.ads 
  SET ctr = CASE 
    WHEN impressions > 0 THEN ROUND((clicks::DECIMAL / impressions::DECIMAL) * 100, 2)
    ELSE 0.00
  END,
  cost_per_click = CASE 
    WHEN clicks > 0 THEN ROUND(budget_spent::DECIMAL / clicks::DECIMAL / 100, 2)
    ELSE 0.00
  END,
  updated_at = NOW()
  WHERE id = ad_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get ads for specific placement with rotation
CREATE OR REPLACE FUNCTION get_ads_for_placement(
  position_param TEXT,
  page_type_param TEXT DEFAULT 'homepage',
  device_type_param TEXT DEFAULT 'desktop',
  limit_param INTEGER DEFAULT 3
) RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  image_url TEXT,
  target_url TEXT,
  ad_type TEXT,
  priority INTEGER,
  impressions INTEGER,
  clicks INTEGER,
  ctr DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.title,
    a.description,
    a.image_url,
    a.target_url,
    a.ad_type,
    a.priority,
    a.impressions,
    a.clicks,
    a.ctr
  FROM public.ads a
  JOIN public.ad_placement_rules apr ON apr.position = position_param
  WHERE a.status = 'active'
    AND a.position = position_param
    AND (a.start_date <= NOW())
    AND (a.end_date IS NULL OR a.end_date >= NOW())
    AND (apr.page_types IS NULL OR page_type_param = ANY(apr.page_types))
    AND (apr.device_types IS NULL OR device_type_param = ANY(apr.device_types))
  ORDER BY 
    a.priority DESC,
    RANDOM() -- Add randomization for rotation
  LIMIT limit_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies
ALTER TABLE public.ad_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ad_impressions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ad_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ad_performance_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ad_placement_rules ENABLE ROW LEVEL SECURITY;

-- Ad clicks policies
CREATE POLICY "Anyone can insert ad clicks" ON public.ad_clicks
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Business owners can view their ad clicks" ON public.ad_clicks
  FOR SELECT USING (
    ad_id IN (
      SELECT id FROM public.ads 
      WHERE business_id IN (
        SELECT id FROM public.business_listings 
        WHERE user_id = auth.uid()
      )
    )
  );

-- Ad impressions policies
CREATE POLICY "Anyone can insert ad impressions" ON public.ad_impressions
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Business owners can view their ad impressions" ON public.ad_impressions
  FOR SELECT USING (
    ad_id IN (
      SELECT id FROM public.ads 
      WHERE business_id IN (
        SELECT id FROM public.business_listings 
        WHERE user_id = auth.uid()
      )
    )
  );

-- Ad campaigns policies
CREATE POLICY "Business owners can manage their campaigns" ON public.ad_campaigns
  FOR ALL USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

-- Performance summary policies
CREATE POLICY "Business owners can view their performance" ON public.ad_performance_summary
  FOR SELECT USING (
    ad_id IN (
      SELECT id FROM public.ads 
      WHERE business_id IN (
        SELECT id FROM public.business_listings 
        WHERE user_id = auth.uid()
      )
    )
  );

-- Placement rules are public
CREATE POLICY "Anyone can view placement rules" ON public.ad_placement_rules
  FOR SELECT USING (true);

-- Create indexes for performance
CREATE INDEX idx_ad_clicks_ad_id ON public.ad_clicks(ad_id);
CREATE INDEX idx_ad_clicks_clicked_at ON public.ad_clicks(clicked_at);
CREATE INDEX idx_ad_clicks_device_type ON public.ad_clicks(device_type);
CREATE INDEX idx_ad_impressions_ad_id ON public.ad_impressions(ad_id);
CREATE INDEX idx_ad_impressions_viewed_at ON public.ad_impressions(viewed_at);
CREATE INDEX idx_ad_impressions_device_type ON public.ad_impressions(device_type);
CREATE INDEX idx_ad_campaigns_business_id ON public.ad_campaigns(business_id);
CREATE INDEX idx_ad_campaigns_status ON public.ad_campaigns(status);
CREATE INDEX idx_ad_performance_summary_ad_id_date ON public.ad_performance_summary(ad_id, date);
CREATE INDEX idx_ads_position_status ON public.ads(position, status);
CREATE INDEX idx_ads_priority ON public.ads(priority DESC);
