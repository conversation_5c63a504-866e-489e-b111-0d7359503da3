
import React, { useState } from "react";
import { MessageCircle, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import AIGuideChat from "./AIGuideChat";

const AIGuideButton = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {isChatOpen ? (
        <div className="flex flex-col items-end">
          <AIGuideChat onClose={() => setIsChatOpen(false)} />
          <Button
            onClick={toggleChat}
            className="bg-[#003087] hover:bg-[#002670] mt-3 h-14 w-14 rounded-full shadow-lg"
            size="icon"
          >
            <X className="h-6 w-6" />
          </Button>
        </div>
      ) : (
        <div className="relative">
          <div className="absolute -top-2 -right-2 w-5 h-5 bg-red-600 rounded-full animate-pulse"></div>
          <Button
            onClick={toggleChat}
            className="bg-[#007749] hover:bg-[#006739] h-14 w-14 rounded-full shadow-lg flex items-center justify-center"
            size="icon"
          >
            <MessageCircle className="h-6 w-6" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default AIGuideButton;
