
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Crown, Star, Gem } from 'lucide-react';

interface PremiumBadgeProps {
  tier: string;
  className?: string;
}

const PremiumBadge = ({ tier, className }: PremiumBadgeProps) => {
  const getTierConfig = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return {
          icon: <Crown className="h-3 w-3" />,
          label: 'Platinum',
          className: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0',
        };
      case 'gold':
        return {
          icon: <Gem className="h-3 w-3" />,
          label: 'Gold',
          className: 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0',
        };
      case 'silver':
        return {
          icon: <Star className="h-3 w-3" />,
          label: 'Silver',
          className: 'bg-gradient-to-r from-gray-400 to-gray-600 text-white border-0',
        };
      default:
        return null;
    }
  };

  const config = getTierConfig(tier);
  
  if (!config) return null;

  return (
    <Badge className={`${config.className} ${className} flex items-center gap-1`}>
      {config.icon}
      {config.label}
    </Badge>
  );
};

export default PremiumBadge;
