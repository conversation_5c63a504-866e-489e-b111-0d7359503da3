-- Create language preferences table
CREATE TABLE public.user_language_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  preferred_language TEXT DEFAULT 'en' CHECK (preferred_language IN ('en', 'af', 'zu')),
  secondary_language TEXT CHECK (secondary_language IN ('en', 'af', 'zu')),
  auto_translate BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create translations table for business listings
CREATE TABLE public.business_listing_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES public.business_listings(id) ON DELETE CASCADE,
  language_code TEXT NOT NULL CHECK (language_code IN ('en', 'af', 'zu')),
  title TEXT,
  description TEXT,
  translated_by TEXT DEFAULT 'auto', -- 'auto', 'human', 'business_owner'
  translation_quality DECIMAL(3,2) DEFAULT 0.85, -- Quality score 0-1
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(business_id, language_code)
);

-- Create translations table for categories
CREATE TABLE public.category_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE,
  language_code TEXT NOT NULL CHECK (language_code IN ('en', 'af', 'zu')),
  name TEXT NOT NULL,
  description TEXT,
  translated_by TEXT DEFAULT 'human',
  is_verified BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(category_id, language_code)
);

-- Create translations table for events
CREATE TABLE public.event_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  language_code TEXT NOT NULL CHECK (language_code IN ('en', 'af', 'zu')),
  title TEXT,
  description TEXT,
  translated_by TEXT DEFAULT 'auto',
  translation_quality DECIMAL(3,2) DEFAULT 0.85,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(event_id, language_code)
);

-- Create UI translations table for interface elements
CREATE TABLE public.ui_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  translation_key TEXT NOT NULL,
  language_code TEXT NOT NULL CHECK (language_code IN ('en', 'af', 'zu')),
  translation_value TEXT NOT NULL,
  context TEXT, -- Page or component context
  is_verified BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(translation_key, language_code)
);

-- Insert category translations
INSERT INTO public.category_translations (category_id, language_code, name, description) VALUES
-- Restaurants
((SELECT id FROM public.categories WHERE slug = 'restaurant' LIMIT 1), 'af', 'Restaurante', 'Eetplekke en restaurante'),
((SELECT id FROM public.categories WHERE slug = 'restaurant' LIMIT 1), 'zu', 'Izindawo zokudlela', 'Izindawo zokudlela nezitolo zokudla'),

-- Accommodation
((SELECT id FROM public.categories WHERE slug = 'accommodation' LIMIT 1), 'af', 'Akkommodasie', 'Hotelle, gastehuis en verblyf'),
((SELECT id FROM public.categories WHERE slug = 'accommodation' LIMIT 1), 'zu', 'Indawo yokuhlala', 'Amahhotela nezindawo zokuhlala'),

-- Professional Services
((SELECT id FROM public.categories WHERE slug = 'professional-services' LIMIT 1), 'af', 'Professionele Dienste', 'Regs-, rekeningkundige en konsultasiedienste'),
((SELECT id FROM public.categories WHERE slug = 'professional-services' LIMIT 1), 'zu', 'Izinsizakalo Zobuchwepheshe', 'Izinsizakalo zomthetho, ze-accounting nezokweluleka'),

-- Retail
((SELECT id FROM public.categories WHERE slug = 'retail' LIMIT 1), 'af', 'Kleinhandel', 'Winkels en kleinhandelaars'),
((SELECT id FROM public.categories WHERE slug = 'retail' LIMIT 1), 'zu', 'Ukuthengisa', 'Izitolo nezindawo zokuthengisa'),

-- Healthcare
((SELECT id FROM public.categories WHERE slug = 'healthcare' LIMIT 1), 'af', 'Gesondheidsorg', 'Mediese dienste en gesondheidsorg'),
((SELECT id FROM public.categories WHERE slug = 'healthcare' LIMIT 1), 'zu', 'Ezempilo', 'Izinsizakalo zezempilo nezokwelapha'),

-- Automotive
((SELECT id FROM public.categories WHERE slug = 'automotive' LIMIT 1), 'af', 'Motorvoertuie', 'Motorhandelaars en herstelwerk'),
((SELECT id FROM public.categories WHERE slug = 'automotive' LIMIT 1), 'zu', 'Izimoto', 'Ukuthengisa nokusebenza kwezimoto'),

-- Technology
((SELECT id FROM public.categories WHERE slug = 'technology' LIMIT 1), 'af', 'Tegnologie', 'IT-dienste en tegnologie-oplossings'),
((SELECT id FROM public.categories WHERE slug = 'technology' LIMIT 1), 'zu', 'Ubuchwepheshe', 'Izinsizakalo ze-IT nobuchwepheshe');

-- Insert common UI translations
INSERT INTO public.ui_translations (translation_key, language_code, translation_value, context) VALUES
-- Navigation
('nav.home', 'af', 'Tuis', 'navigation'),
('nav.home', 'zu', 'Ikhaya', 'navigation'),
('nav.businesses', 'af', 'Besighede', 'navigation'),
('nav.businesses', 'zu', 'Amabhizinisi', 'navigation'),
('nav.events', 'af', 'Geleenthede', 'navigation'),
('nav.events', 'zu', 'Imicimbi', 'navigation'),
('nav.services', 'af', 'Dienste', 'navigation'),
('nav.services', 'zu', 'Izinsizakalo', 'navigation'),

-- Common buttons
('button.search', 'af', 'Soek', 'button'),
('button.search', 'zu', 'Sesha', 'button'),
('button.submit', 'af', 'Indien', 'button'),
('button.submit', 'zu', 'Thumela', 'button'),
('button.cancel', 'af', 'Kanselleer', 'button'),
('button.cancel', 'zu', 'Khansela', 'button'),
('button.save', 'af', 'Stoor', 'button'),
('button.save', 'zu', 'Gcina', 'button'),
('button.edit', 'af', 'Wysig', 'button'),
('button.edit', 'zu', 'Hlela', 'button'),
('button.delete', 'af', 'Skrap', 'button'),
('button.delete', 'zu', 'Susa', 'button'),

-- Common labels
('label.name', 'af', 'Naam', 'form'),
('label.name', 'zu', 'Igama', 'form'),
('label.email', 'af', 'E-pos', 'form'),
('label.email', 'zu', 'I-imeyili', 'form'),
('label.phone', 'af', 'Telefoon', 'form'),
('label.phone', 'zu', 'Ifoni', 'form'),
('label.address', 'af', 'Adres', 'form'),
('label.address', 'zu', 'Ikheli', 'form'),
('label.city', 'af', 'Stad', 'form'),
('label.city', 'zu', 'Idolobha', 'form'),
('label.description', 'af', 'Beskrywing', 'form'),
('label.description', 'zu', 'Incazelo', 'form'),

-- Business related
('business.featured', 'af', 'Uitgelig', 'business'),
('business.featured', 'zu', 'Okuvelele', 'business'),
('business.verified', 'af', 'Geverifieer', 'business'),
('business.verified', 'zu', 'Kuqinisekisiwe', 'business'),
('business.premium', 'af', 'Premium', 'business'),
('business.premium', 'zu', 'I-Premium', 'business'),
('business.contact', 'af', 'Kontak', 'business'),
('business.contact', 'zu', 'Xhumana', 'business'),
('business.website', 'af', 'Webwerf', 'business'),
('business.website', 'zu', 'Iwebhusayithi', 'business'),
('business.reviews', 'af', 'Resensies', 'business'),
('business.reviews', 'zu', 'Ukubuyekezwa', 'business'),
('business.rating', 'af', 'Gradering', 'business'),
('business.rating', 'zu', 'Isilinganiso', 'business'),

-- Location names
('location.cape_town', 'af', 'Kaapstad', 'location'),
('location.cape_town', 'zu', 'iKapa', 'location'),
('location.johannesburg', 'af', 'Johannesburg', 'location'),
('location.johannesburg', 'zu', 'eGoli', 'location'),
('location.durban', 'af', 'Durban', 'location'),
('location.durban', 'zu', 'eThekwini', 'location'),
('location.pretoria', 'af', 'Pretoria', 'location'),
('location.pretoria', 'zu', 'ePitoli', 'location'),

-- Messages
('message.loading', 'af', 'Laai...', 'message'),
('message.loading', 'zu', 'Iyalayisha...', 'message'),
('message.no_results', 'af', 'Geen resultate gevind nie', 'message'),
('message.no_results', 'zu', 'Ayikho imiphumela etholiwe', 'message'),
('message.error', 'af', 'Fout opgetree', 'message'),
('message.error', 'zu', 'Kube khona iphutha', 'message'),
('message.success', 'af', 'Suksesvol', 'message'),
('message.success', 'zu', 'Kuphumelele', 'message');

-- Function to get translation for a key
CREATE OR REPLACE FUNCTION get_translation(
  translation_key_param TEXT,
  language_code_param TEXT DEFAULT 'en',
  fallback_value TEXT DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
  translation_result TEXT;
BEGIN
  -- Try to get translation for requested language
  SELECT translation_value INTO translation_result
  FROM public.ui_translations
  WHERE translation_key = translation_key_param
    AND language_code = language_code_param;
  
  -- If not found, try English as fallback
  IF translation_result IS NULL AND language_code_param != 'en' THEN
    SELECT translation_value INTO translation_result
    FROM public.ui_translations
    WHERE translation_key = translation_key_param
      AND language_code = 'en';
  END IF;
  
  -- If still not found, return fallback value or key
  IF translation_result IS NULL THEN
    translation_result := COALESCE(fallback_value, translation_key_param);
  END IF;
  
  RETURN translation_result;
END;
$$ LANGUAGE plpgsql;

-- Function to get business listing with translations
CREATE OR REPLACE FUNCTION get_business_with_translation(
  business_id_param UUID,
  language_code_param TEXT DEFAULT 'en'
) RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  original_title TEXT,
  original_description TEXT,
  translation_quality DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    bl.id,
    COALESCE(blt.title, bl.title) as title,
    COALESCE(blt.description, bl.description) as description,
    bl.title as original_title,
    bl.description as original_description,
    COALESCE(blt.translation_quality, 1.0) as translation_quality
  FROM public.business_listings bl
  LEFT JOIN public.business_listing_translations blt 
    ON bl.id = blt.business_id 
    AND blt.language_code = language_code_param
  WHERE bl.id = business_id_param;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies
ALTER TABLE public.user_language_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_listing_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.category_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ui_translations ENABLE ROW LEVEL SECURITY;

-- Language preferences policies
CREATE POLICY "Users can manage their language preferences" ON public.user_language_preferences
  FOR ALL USING (auth.uid() = user_id);

-- Translation policies
CREATE POLICY "Anyone can view translations" ON public.business_listing_translations
  FOR SELECT USING (true);

CREATE POLICY "Business owners can manage their translations" ON public.business_listing_translations
  FOR ALL USING (
    business_id IN (
      SELECT id FROM public.business_listings 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Anyone can view category translations" ON public.category_translations
  FOR SELECT USING (true);

CREATE POLICY "Anyone can view event translations" ON public.event_translations
  FOR SELECT USING (true);

CREATE POLICY "Anyone can view UI translations" ON public.ui_translations
  FOR SELECT USING (true);

-- Create indexes for performance
CREATE INDEX idx_user_language_preferences_user_id ON public.user_language_preferences(user_id);
CREATE INDEX idx_business_listing_translations_business_id ON public.business_listing_translations(business_id);
CREATE INDEX idx_business_listing_translations_language ON public.business_listing_translations(language_code);
CREATE INDEX idx_category_translations_category_id ON public.category_translations(category_id);
CREATE INDEX idx_category_translations_language ON public.category_translations(language_code);
CREATE INDEX idx_event_translations_event_id ON public.event_translations(event_id);
CREATE INDEX idx_event_translations_language ON public.event_translations(language_code);
CREATE INDEX idx_ui_translations_key_language ON public.ui_translations(translation_key, language_code);
