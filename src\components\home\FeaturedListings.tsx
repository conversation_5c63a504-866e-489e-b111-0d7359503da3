
import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { useBusinessListings } from "@/hooks/useBusinessListings";
import BusinessCard from "@/components/business/BusinessCard";

const FeaturedListings = () => {
  const { data: businesses, isLoading, error } = useBusinessListings({ 
    limit: 4 
  });

  if (isLoading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-slate-800">
              Premium Featured Businesses
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-80"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    console.error('Error loading featured listings:', error);
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-slate-800 mb-4">
              Premium Featured Businesses
            </h2>
            <p className="text-slate-600">Unable to load businesses at the moment.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-slate-800">
            Premium Featured Businesses
          </h2>
          <Link to="/businesses" className="text-[#003087] hover:text-[#007749] font-medium hidden md:block">
            View All
          </Link>
        </div>

        {businesses && businesses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {businesses.map((business) => (
              <BusinessCard 
                key={business.id} 
                business={business}
                featured={business.featured || false}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-slate-600 mb-4">No featured businesses available yet.</p>
            <Link to="/add-listing">
              <Button className="bg-[#007749] hover:bg-[#006739]">
                Be the First to List Your Business
              </Button>
            </Link>
          </div>
        )}

        <div className="mt-8 text-center md:hidden">
          <Button variant="outline" asChild>
            <Link to="/businesses">View All Businesses</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedListings;
