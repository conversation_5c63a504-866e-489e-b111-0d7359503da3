import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import ServiceRequestForm from './ServiceRequestForm';
import {
  Star,
  MapPin,
  Globe,
  Mail,
  Phone,
  Search,
  Filter,
  Verified,
  Award,
  Plus
} from 'lucide-react';

interface ServiceProvider {
  id: string;
  business_name: string;
  service_category: string;
  description: string;
  specialties: string[];
  portfolio_url: string | null;
  pricing_model: string;
  price_range: string | null;
  location: string | null;
  contact_email: string;
  contact_phone: string | null;
  website_url: string | null;
  logo_url: string | null;
  rating: number;
  review_count: number;
  verified: boolean;
  featured: boolean;
}

const ServiceProviderDirectory = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [showRequestForm, setShowRequestForm] = useState(false);

  const { data: providers, isLoading } = useQuery({
    queryKey: ['service-providers', searchTerm, selectedCategory, selectedLocation],
    queryFn: async () => {
      let query = supabase
        .from('service_providers')
        .select('*')
        .eq('status', 'approved')
        .order('featured', { ascending: false })
        .order('rating', { ascending: false })
        .order('review_count', { ascending: false });

      if (searchTerm) {
        query = query.or(`business_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      if (selectedCategory !== 'all') {
        query = query.eq('service_category', selectedCategory);
      }

      if (selectedLocation !== 'all') {
        query = query.ilike('location', `%${selectedLocation}%`);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as ServiceProvider[];
    },
  });

  const serviceCategories = [
    { value: 'all', label: 'All Services' },
    { value: 'web_development', label: 'Web Development' },
    { value: 'photography', label: 'Photography' },
    { value: 'seo', label: 'SEO & Marketing' },
    { value: 'graphic_design', label: 'Graphic Design' },
    { value: 'content_writing', label: 'Content Writing' },
    { value: 'social_media', label: 'Social Media Management' },
    { value: 'accounting', label: 'Accounting & Bookkeeping' },
    { value: 'legal', label: 'Legal Services' },
    { value: 'consulting', label: 'Business Consulting' }
  ];

  const locations = [
    { value: 'all', label: 'All Locations' },
    { value: 'Cape Town', label: 'Cape Town' },
    { value: 'Johannesburg', label: 'Johannesburg' },
    { value: 'Durban', label: 'Durban' },
    { value: 'Pretoria', label: 'Pretoria' },
    { value: 'Port Elizabeth', label: 'Port Elizabeth' },
    { value: 'Remote', label: 'Remote/Online' }
  ];

  const handleContactProvider = (provider: ServiceProvider) => {
    const subject = `Service Inquiry from SA360 Directory`;
    const body = `Hi ${provider.business_name},\n\nI found your services on SA360 and would like to discuss a potential project.\n\nBest regards`;
    window.location.href = `mailto:${provider.contact_email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-48"></div>
        ))}
      </div>
    );
  }

  if (showRequestForm) {
    return (
      <ServiceRequestForm
        onClose={() => setShowRequestForm(false)}
        onSuccess={() => setShowRequestForm(false)}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Find Service Providers</CardTitle>
              <CardDescription>
                Connect with verified professionals to grow your business
              </CardDescription>
            </div>
            <Button
              onClick={() => setShowRequestForm(true)}
              className="bg-[#007749] hover:bg-[#006739]"
            >
              <Plus className="h-4 w-4 mr-2" />
              Post Service Request
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="Search providers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Service Category" />
              </SelectTrigger>
              <SelectContent>
                {serviceCategories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedLocation} onValueChange={setSelectedLocation}>
              <SelectTrigger>
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location.value} value={location.value}>
                    {location.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button 
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedLocation('all');
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="space-y-4">
        {providers && providers.length > 0 ? (
          <>
            <p className="text-slate-600">
              Found {providers.length} service provider{providers.length !== 1 ? 's' : ''}
            </p>
            
            {providers.map((provider) => (
              <Card key={provider.id} className={`${provider.featured ? 'ring-2 ring-[#FDB913] ring-opacity-50' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      {provider.logo_url ? (
                        <img 
                          src={provider.logo_url} 
                          alt={provider.business_name}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-slate-100 rounded-lg flex items-center justify-center">
                          <Award className="h-8 w-8 text-slate-400" />
                        </div>
                      )}

                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-semibold text-slate-800">
                            {provider.business_name}
                          </h3>
                          {provider.featured && (
                            <Badge className="bg-[#FDB913] text-black">Featured</Badge>
                          )}
                          {provider.verified && (
                            <Badge className="bg-green-100 text-green-800">
                              <Verified className="h-3 w-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-4 mb-3 text-sm text-slate-600">
                          <Badge variant="outline" className="capitalize">
                            {provider.service_category.replace('_', ' ')}
                          </Badge>
                          
                          {provider.rating > 0 && (
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">{provider.rating.toFixed(1)}</span>
                              <span>({provider.review_count} reviews)</span>
                            </div>
                          )}

                          {provider.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-4 w-4" />
                              {provider.location}
                            </div>
                          )}
                        </div>

                        <p className="text-slate-700 mb-3 line-clamp-2">
                          {provider.description}
                        </p>

                        {provider.specialties && provider.specialties.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {provider.specialties.slice(0, 4).map((specialty, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {specialty}
                              </Badge>
                            ))}
                            {provider.specialties.length > 4 && (
                              <Badge variant="secondary" className="text-xs">
                                +{provider.specialties.length - 4} more
                              </Badge>
                            )}
                          </div>
                        )}

                        <div className="flex items-center gap-4 text-sm text-slate-600">
                          <span className="capitalize">
                            <strong>Pricing:</strong> {provider.pricing_model}
                            {provider.price_range && ` • ${provider.price_range}`}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      <Button 
                        onClick={() => handleContactProvider(provider)}
                        className="bg-[#007749] hover:bg-[#006739]"
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Contact
                      </Button>
                      
                      {provider.website_url && (
                        <Button 
                          variant="outline"
                          onClick={() => window.open(provider.website_url!, '_blank')}
                        >
                          <Globe className="h-4 w-4 mr-2" />
                          Website
                        </Button>
                      )}

                      {provider.portfolio_url && (
                        <Button 
                          variant="outline"
                          onClick={() => window.open(provider.portfolio_url!, '_blank')}
                        >
                          Portfolio
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </>
        ) : (
          <div className="text-center py-12">
            <Award className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 mb-4">No service providers found matching your criteria.</p>
            <Button 
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedLocation('all');
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ServiceProviderDirectory;
