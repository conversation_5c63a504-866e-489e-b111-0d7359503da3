import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Check, CreditCard, Globe } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

const pricingPlans = [
  {
    name: "Free Basic",
    price: "R0",
    priceUSD: "$0",
    period: "/month",
    description: "Perfect for small businesses just getting started",
    features: [
      "Basic business information",
      "Single photo upload",
      "Contact information",
      "Standard search placement",
      "Basic analytics",
    ],
    buttonText: "Start for Free",
    buttonVariant: "outline" as const,
    popular: false,
    tier: "free"
  },
  {
    name: "Silver Premium",
    price: "R499",
    priceUSD: "$29.99",
    period: "/month",
    description: "For businesses looking to enhance their online presence",
    features: [
      "Enhanced visibility",
      "Gallery with up to 10 photos",
      "Detailed business description",
      "Business hours display",
      "Social media links",
      "Customer review management",
      "Enhanced analytics"
    ],
    buttonText: "Subscribe Now",
    buttonVariant: "default" as const,
    popular: false,
    tier: "silver"
  },
  {
    name: "Gold Premium",
    price: "R899",
    priceUSD: "$54.99",
    period: "/month",
    description: "For established businesses seeking maximum exposure",
    features: [
      "Featured placement in search",
      "Verified business badge",
      "Unlimited photo gallery",
      "Video integration",
      "Priority support",
      "Advanced analytics dashboard", 
      "Promotional badges",
      "Competitor insights",
      "Monthly performance report"
    ],
    buttonText: "Get Started",
    buttonVariant: "default" as const,
    popular: true,
    tier: "gold"
  },
  {
    name: "Platinum Premium",
    price: "R1499",
    priceUSD: "$89.99",
    period: "/month",
    description: "Complete visibility and marketing solution",
    features: [
      "Top of search results",
      "Premium listing badge",
      "Featured on homepage",
      "Unlimited media",
      "Dedicated account manager",
      "SEO optimization",
      "Premium analytics suite",
      "Promotional campaigns",
      "Weekly performance reports",
      "Direct leads notification"
    ],
    buttonText: "Contact Sales",
    buttonVariant: "default" as const,
    popular: false,
    tier: "platinum"
  }
];

const Pricing = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<"payfast" | "paypal">("payfast");

  const handleSubscribe = async (tier: string) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to subscribe to a plan.",
        variant: "destructive",
      });
      return;
    }

    if (tier === "free") {
      return;
    }

    setLoading(tier);

    try {
      // Get user's business listings to associate with subscription
      const { data: listings } = await supabase
        .from("business_listings")
        .select("id")
        .eq("user_id", user.id)
        .limit(1);

      const businessId = listings?.[0]?.id;
      
      if (!businessId) {
        toast({
          title: "No Business Listing Found",
          description: "Please create a business listing first before subscribing.",
          variant: "destructive",
        });
        setLoading(null);
        return;
      }

      if (paymentMethod === "payfast") {
        // PayFast integration
        const { data, error } = await supabase.functions.invoke('create-payfast-subscription', {
          body: { tier, businessId }
        });

        if (error) throw error;

        // PayFast returns HTML form that auto-submits
        const newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.document.write(data);
          newWindow.document.close();
        }
      } else {
        // PayPal integration
        const { data, error } = await supabase.functions.invoke('create-paypal-subscription', {
          body: { tier, businessId }
        });

        if (error) throw error;

        if (data.approvalUrl) {
          window.location.href = data.approvalUrl;
        }
      }

    } catch (error) {
      console.error('Subscription error:', error);
      toast({
        title: "Subscription Error",
        description: "Failed to process subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(null);
    }
  };

  return (
    <Layout>
      <div className="py-12 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">Pricing Plans</h1>
            <p className="text-slate-600 mb-6">
              Choose the perfect plan for your business needs. Upgrade or downgrade at any time.
            </p>
            
            {/* Payment Method Toggle */}
            <div className="flex justify-center mb-8">
              <div className="bg-white p-1 rounded-lg shadow-sm border">
                <button
                  onClick={() => setPaymentMethod("payfast")}
                  className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                    paymentMethod === "payfast" 
                      ? "bg-[#007749] text-white" 
                      : "text-slate-600 hover:bg-slate-100"
                  }`}
                >
                  <CreditCard className="h-4 w-4" />
                  PayFast (ZAR)
                </button>
                <button
                  onClick={() => setPaymentMethod("paypal")}
                  className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                    paymentMethod === "paypal" 
                      ? "bg-[#007749] text-white" 
                      : "text-slate-600 hover:bg-slate-100"
                  }`}
                >
                  <Globe className="h-4 w-4" />
                  PayPal (USD)
                </button>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {pricingPlans.map((plan) => (
              <div 
                key={plan.name} 
                className={`bg-white rounded-xl shadow-md overflow-hidden ${
                  plan.popular ? 'ring-2 ring-[#FDB913] relative' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 bg-[#FDB913] text-black text-xs font-semibold px-3 py-1 rounded-bl-lg">
                    MOST POPULAR
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-slate-800">{plan.name}</h3>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-3xl font-bold text-slate-900">
                      {paymentMethod === "payfast" ? plan.price : plan.priceUSD}
                    </span>
                    <span className="ml-1 text-sm text-slate-500">{plan.period}</span>
                  </div>
                  <p className="mt-3 text-sm text-slate-600">{plan.description}</p>
                  
                  <ul className="mt-6 space-y-3">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-start">
                        <Check className="h-5 w-5 text-[#007749] flex-shrink-0" />
                        <span className="ml-3 text-sm text-slate-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <div className="mt-8">
                    <Button 
                      className={`w-full ${plan.buttonVariant === 'default' ? 'bg-[#007749] hover:bg-[#006739]' : ''}`}
                      variant={plan.buttonVariant}
                      onClick={() => handleSubscribe(plan.tier)}
                      disabled={loading === plan.tier}
                    >
                      {loading === plan.tier ? "Processing..." : plan.buttonText}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-16 max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-md">
            <h2 className="text-2xl font-semibold text-slate-800 mb-4">Frequently Asked Questions</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="font-medium text-slate-900">What payment methods do you accept?</h3>
                <p className="mt-2 text-slate-600">We accept PayFast for local South African payments (ZAR) and PayPal for international customers (USD). You can switch between payment methods on this page.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-slate-900">What's included in the free plan?</h3>
                <p className="mt-2 text-slate-600">The free plan includes basic business information, a single photo, and contact details. It's a great way to get started with SA360.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-slate-900">Can I upgrade or downgrade my plan?</h3>
                <p className="mt-2 text-slate-600">Yes, you can upgrade or downgrade your plan at any time. Changes will take effect at the start of your next billing cycle.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-slate-900">How do I get verified?</h3>
                <p className="mt-2 text-slate-600">Verification is available on our Gold and Platinum plans. Our team will contact you to verify your business details once you subscribe.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-slate-900">Are there any long-term contracts?</h3>
                <p className="mt-2 text-slate-600">No, all our plans are month-to-month with no long-term commitment. You can cancel at any time.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Pricing;
