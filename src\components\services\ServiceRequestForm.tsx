import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { ArrowLeft, Send } from 'lucide-react';

interface ServiceRequestFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

const ServiceRequestForm = ({ onClose, onSuccess }: ServiceRequestFormProps) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    service_category: '',
    title: '',
    description: '',
    budget_range: '',
    timeline: '',
    contact_name: '',
    contact_email: user?.email || '',
    contact_phone: '',
    business_id: '',
    requirements: {
      experience_level: '',
      portfolio_required: false,
      location_preference: '',
      communication_preference: '',
      additional_skills: []
    }
  });

  const serviceCategories = [
    { value: 'web_development', label: 'Web Development' },
    { value: 'photography', label: 'Photography & Videography' },
    { value: 'seo', label: 'SEO & Digital Marketing' },
    { value: 'graphic_design', label: 'Graphic Design' },
    { value: 'content_writing', label: 'Content Writing' },
    { value: 'social_media', label: 'Social Media Management' },
    { value: 'accounting', label: 'Accounting & Bookkeeping' },
    { value: 'legal', label: 'Legal Services' },
    { value: 'consulting', label: 'Business Consulting' },
    { value: 'other', label: 'Other' }
  ];

  const budgetRanges = [
    'Under R5,000',
    'R5,000 - R15,000',
    'R15,000 - R30,000',
    'R30,000 - R50,000',
    'R50,000 - R100,000',
    'R100,000+',
    'Ongoing/Retainer',
    'Prefer to discuss'
  ];

  const timelines = [
    'ASAP (within 1 week)',
    '2-4 weeks',
    '1-2 months',
    '3-6 months',
    '6+ months',
    'Ongoing project',
    'Flexible'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error('Please sign in to submit a service request');
      return;
    }

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('service_requests')
        .insert({
          service_category: formData.service_category,
          title: formData.title,
          description: formData.description,
          budget_range: formData.budget_range || null,
          timeline: formData.timeline || null,
          contact_name: formData.contact_name,
          contact_email: formData.contact_email,
          contact_phone: formData.contact_phone || null,
          business_id: formData.business_id || null,
          requirements: formData.requirements,
          status: 'open'
        });

      if (error) throw error;

      toast.success('Service request submitted successfully! Providers will contact you soon.');
      onSuccess();
    } catch (error) {
      console.error('Error submitting service request:', error);
      toast.error('Failed to submit service request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onClose}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5 text-[#007749]" />
                Post Service Request
              </CardTitle>
              <CardDescription>
                Describe your project and get proposals from qualified service providers
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="service_category">Service Category *</Label>
                <Select 
                  value={formData.service_category} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, service_category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select service category" />
                  </SelectTrigger>
                  <SelectContent>
                    {serviceCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="budget_range">Budget Range</Label>
                <Select 
                  value={formData.budget_range} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, budget_range: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgetRanges.map((range) => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="title">Project Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                required
                placeholder="Brief title for your project"
                maxLength={100}
              />
            </div>

            <div>
              <Label htmlFor="description">Project Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                required
                placeholder="Describe your project requirements, goals, and any specific needs..."
                rows={5}
                maxLength={1000}
              />
            </div>

            <div>
              <Label htmlFor="timeline">Timeline</Label>
              <Select 
                value={formData.timeline} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, timeline: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select timeline" />
                </SelectTrigger>
                <SelectContent>
                  {timelines.map((timeline) => (
                    <SelectItem key={timeline} value={timeline}>
                      {timeline}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contact_name">Contact Name *</Label>
                <Input
                  id="contact_name"
                  value={formData.contact_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, contact_name: e.target.value }))}
                  required
                  placeholder="Your full name"
                />
              </div>

              <div>
                <Label htmlFor="contact_phone">Contact Phone</Label>
                <Input
                  id="contact_phone"
                  type="tel"
                  value={formData.contact_phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, contact_phone: e.target.value }))}
                  placeholder="+27 XX XXX XXXX"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="contact_email">Contact Email *</Label>
              <Input
                id="contact_email"
                type="email"
                value={formData.contact_email}
                onChange={(e) => setFormData(prev => ({ ...prev, contact_email: e.target.value }))}
                required
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Additional Requirements</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Experience Level Preference</Label>
                  <Select 
                    value={formData.requirements.experience_level} 
                    onValueChange={(value) => setFormData(prev => ({ 
                      ...prev, 
                      requirements: { ...prev.requirements, experience_level: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="entry">Entry Level</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="expert">Expert/Senior</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Location Preference</Label>
                  <Select 
                    value={formData.requirements.location_preference} 
                    onValueChange={(value) => setFormData(prev => ({ 
                      ...prev, 
                      requirements: { ...prev.requirements, location_preference: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="No preference" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="local">Local/Same city</SelectItem>
                      <SelectItem value="national">South Africa</SelectItem>
                      <SelectItem value="remote">Remote OK</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="portfolio_required"
                  checked={formData.requirements.portfolio_required}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    requirements: { ...prev.requirements, portfolio_required: checked as boolean }
                  }))}
                />
                <Label htmlFor="portfolio_required">
                  Portfolio/Previous work examples required
                </Label>
              </div>
            </div>

            <div className="flex gap-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1 bg-[#007749] hover:bg-[#006739]"
                disabled={isSubmitting || !formData.service_category || !formData.title || !formData.description || !formData.contact_name || !formData.contact_email}
              >
                {isSubmitting ? 'Submitting...' : 'Post Request'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ServiceRequestForm;
