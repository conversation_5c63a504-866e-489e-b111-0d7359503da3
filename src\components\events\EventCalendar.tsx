import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, 
  MapPin, 
  Clock, 
  Users, 
  ExternalLink,
  Star,
  Filter,
  Search,
  Heart,
  HeartOff
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface Event {
  id: string;
  title: string;
  description: string | null;
  event_type: string;
  category: string | null;
  start_date: string;
  end_date: string | null;
  location: string;
  venue_name: string | null;
  city: string;
  organizer_name: string | null;
  website_url: string | null;
  ticket_url: string | null;
  price_range: string | null;
  images: string[] | null;
  tags: string[] | null;
  featured: boolean;
}

const EventCalendar = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCity, setSelectedCity] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedMonth, setSelectedMonth] = useState('all');

  const { data: events, isLoading } = useQuery({
    queryKey: ['events', searchTerm, selectedCity, selectedType, selectedMonth],
    queryFn: async () => {
      let query = supabase
        .from('events')
        .select('*')
        .eq('status', 'published')
        .gte('start_date', new Date().toISOString())
        .order('start_date', { ascending: true });

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      if (selectedCity !== 'all') {
        query = query.eq('city', selectedCity);
      }

      if (selectedType !== 'all') {
        query = query.eq('event_type', selectedType);
      }

      if (selectedMonth !== 'all') {
        const year = new Date().getFullYear();
        const month = parseInt(selectedMonth);
        const startDate = new Date(year, month - 1, 1).toISOString();
        const endDate = new Date(year, month, 0, 23, 59, 59).toISOString();
        query = query.gte('start_date', startDate).lte('start_date', endDate);
      }

      const { data, error } = await query.limit(50);
      if (error) throw error;
      return data as Event[];
    },
  });

  const markInterest = async (eventId: string, interestType: 'interested' | 'attending') => {
    if (!user) {
      toast.error('Please sign in to mark interest in events');
      return;
    }

    try {
      const { error } = await supabase
        .from('event_interests')
        .upsert({
          event_id: eventId,
          user_id: user.id,
          interest_type: interestType
        });

      if (error) throw error;

      toast.success(
        interestType === 'interested' 
          ? 'Added to your interested events!' 
          : 'Marked as attending!'
      );
    } catch (error) {
      console.error('Error marking interest:', error);
      toast.error('Failed to update interest');
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'festival':
        return '🎪';
      case 'conference':
        return '🎤';
      case 'workshop':
        return '🛠️';
      case 'networking':
        return '🤝';
      case 'exhibition':
        return '🏛️';
      case 'concert':
        return '🎵';
      case 'sports':
        return '⚽';
      case 'community':
        return '🏘️';
      default:
        return '📅';
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'festival':
        return 'bg-purple-100 text-purple-800';
      case 'conference':
        return 'bg-blue-100 text-blue-800';
      case 'workshop':
        return 'bg-green-100 text-green-800';
      case 'networking':
        return 'bg-orange-100 text-orange-800';
      case 'exhibition':
        return 'bg-indigo-100 text-indigo-800';
      case 'concert':
        return 'bg-pink-100 text-pink-800';
      case 'sports':
        return 'bg-red-100 text-red-800';
      case 'community':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const cities = ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'];
  const eventTypes = ['festival', 'conference', 'workshop', 'networking', 'exhibition', 'concert', 'sports', 'community'];
  const months = [
    { value: '1', label: 'January' },
    { value: '2', label: 'February' },
    { value: '3', label: 'March' },
    { value: '4', label: 'April' },
    { value: '5', label: 'May' },
    { value: '6', label: 'June' },
    { value: '7', label: 'July' },
    { value: '8', label: 'August' },
    { value: '9', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' }
  ];

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-slate-200 animate-pulse rounded-lg h-48"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-[#007749]" />
            Event Calendar
          </CardTitle>
          <CardDescription>
            Discover local events and business networking opportunities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedCity} onValueChange={setSelectedCity}>
              <SelectTrigger>
                <SelectValue placeholder="All Cities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Cities</SelectItem>
                {cities.map((city) => (
                  <SelectItem key={city} value={city}>
                    {city}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {eventTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {getEventTypeIcon(type)} {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedMonth} onValueChange={setSelectedMonth}>
              <SelectTrigger>
                <SelectValue placeholder="All Months" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Months</SelectItem>
                {months.map((month) => (
                  <SelectItem key={month.value} value={month.value}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button 
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setSelectedCity('all');
                setSelectedType('all');
                setSelectedMonth('all');
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Events List */}
      {!events || events.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 mb-2">No events found</p>
            <p className="text-sm text-slate-500">
              Try adjusting your filters or check back later for new events
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <p className="text-slate-600">
            Found {events.length} upcoming event{events.length !== 1 ? 's' : ''}
          </p>

          {events.map((event) => (
            <Card key={event.id} className={`${event.featured ? 'ring-2 ring-[#FDB913] ring-opacity-50' : ''}`}>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {event.images && event.images.length > 0 ? (
                    <img 
                      src={event.images[0]} 
                      alt={event.title}
                      className="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                    />
                  ) : (
                    <div className="w-24 h-24 bg-slate-100 rounded-lg flex items-center justify-center text-3xl">
                      {getEventTypeIcon(event.event_type)}
                    </div>
                  )}

                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-xl font-semibold text-slate-800">
                            {event.title}
                          </h3>
                          {event.featured && (
                            <Badge className="bg-[#FDB913] text-black">
                              <Star className="h-3 w-3 mr-1" />
                              Featured
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-slate-600 mb-2">
                          <Badge className={getEventTypeColor(event.event_type)}>
                            {getEventTypeIcon(event.event_type)} {event.event_type}
                          </Badge>
                          
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {formatDate(event.start_date)}
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {event.venue_name ? `${event.venue_name}, ` : ''}{event.city}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => markInterest(event.id, 'interested')}
                          className="text-slate-600 hover:text-red-600"
                        >
                          <Heart className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => markInterest(event.id, 'attending')}
                        >
                          <Users className="h-4 w-4 mr-1" />
                          Attending
                        </Button>
                      </div>
                    </div>

                    {event.description && (
                      <p className="text-slate-700 mb-3 line-clamp-2">
                        {event.description}
                      </p>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-slate-600">
                        {event.organizer_name && (
                          <span>Organized by {event.organizer_name}</span>
                        )}
                        {event.price_range && (
                          <span className="font-medium text-[#007749]">
                            {event.price_range}
                          </span>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        {event.website_url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(event.website_url!, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            Website
                          </Button>
                        )}
                        {event.ticket_url && (
                          <Button
                            size="sm"
                            onClick={() => window.open(event.ticket_url!, '_blank')}
                            className="bg-[#007749] hover:bg-[#006739]"
                          >
                            Get Tickets
                          </Button>
                        )}
                      </div>
                    </div>

                    {event.tags && event.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {event.tags.slice(0, 5).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default EventCalendar;
